'use client';

import Header from '../components/Header';
import Footer from '../components/Footer';
import { useEffect, useState } from 'react';

interface Article {
  id: number;
  title: string;
  slug: string;
  content?: string;
  excerpt: string;
  author: string;
  category: string;
  created_at: string;
  published_at: string;
  site_code: string;
}

interface Category {
  category: string;
  count: number;
}

export default function Makalelerimiz() {
  const [articles, setArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedArticle, setSelectedArticle] = useState<Article | null>(null);

  const articlesPerPage = 12;

  useEffect(() => {
    // Build sırasında API çağrısı yapma
    if (typeof window !== 'undefined') {
      fetchArticles();
    }
  }, [currentPage]);

  const fetchArticles = async () => {
    try {
      setLoading(true);

      const offset = (currentPage - 1) * articlesPerPage;
      const params = new URLSearchParams({
        site_code: 'mgsam',
        limit: articlesPerPage.toString(),
        offset: offset.toString(),
      });

      const response = await fetch(`/api/articles.php?${params}`);

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          setArticles(data.data.articles || []);
          setTotalPages(Math.ceil((data.data.total || 0) / articlesPerPage));
        } else {
          setArticles([]);
        }
      } else {
        setArticles([]);
        setTotalPages(1);
      }
    } catch (error) {
      console.error('Fetch error:', error);
      setArticles([]);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };



  const handleArticleClick = async (article: Article) => {
    // Makale detayını fetch et
    try {
      setLoading(true);
      const response = await fetch(`/api/articles.php?slug=${article.slug}&site_code=mgsam`);

      if (response.ok) {
        const data = await response.json();
        console.log('Single article response:', data);

        if (data.success && data.data) {
          if (data.data.id) {
            setSelectedArticle(data.data);
          } else if (data.data.articles && data.data.articles.length > 0) {
            setSelectedArticle(data.data.articles[0]);
          }
        }
      }
    } catch (error) {
      console.error('Error fetching single article:', error);
    } finally {
      setLoading(false);
    }
  };



  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };



  // Handle pagination
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };







  if (loading) {
    return (
      <main className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
        <Header currentPage="makalelerimiz" />
        <div className="pt-32 pb-16">
          <div className="container mx-auto px-6">
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Makaleler yükleniyor...</p>
            </div>
          </div>
        </div>
        <Footer />
      </main>
    );
  }

  // Eğer makale detayı gösteriliyorsa
  if (selectedArticle) {
    return (
      <main className="min-h-screen bg-gray-50">
        <Header />

        {/* Hero Section */}
        <section className="relative bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 py-20">
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="container mx-auto px-6 relative z-10">
            <div className="max-w-4xl mx-auto">
              {/* Back Button - Left Aligned */}
              <div className="mb-8">
                <button
                  onClick={() => setSelectedArticle(null)}
                  className="inline-flex items-center px-6 py-3 bg-white/10 text-white rounded-lg hover:bg-white/20 transition-all duration-300 border border-white/20"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                  </svg>
                  Makalelere Dön
                </button>
              </div>

              {/* Content - Centered */}
              <div className="text-center">
                <div className="inline-flex items-center px-4 py-2 bg-white/10 rounded-full text-white text-sm font-medium mb-6 border border-white/20">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                  Makalelerimiz
                </div>

                <h1 className="text-xl md:text-3xl font-light text-white mb-6 leading-snug md:leading-normal">
                  {selectedArticle.title}
                </h1>

                <div className="flex items-center justify-center space-x-6 text-white/80">
                  <div className="flex items-center">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    <span>{selectedArticle.author}</span>
                  </div>
                  <div className="flex items-center">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span>{formatDate(selectedArticle.published_at || selectedArticle.created_at)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Article Content */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto">
              {selectedArticle.content ? (
                <div
                  className="prose prose-lg max-w-none text-gray-800 leading-relaxed article-content markdown-content"
                  style={{
                    lineHeight: '1.7',
                  }}
                  dangerouslySetInnerHTML={{
                    __html: selectedArticle.content
                      // URL'leri tıklanabilir linkler haline getir
                      .replace(/(https?:\/\/[^\s)]+)/g, '<a href="$1" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:text-blue-800 underline">$1</a>')
                      // Markdown formatlamasını HTML'e çevir
                      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // **kalın** -> <strong>
                      .replace(/\*(.*?)\*/g, '<em>$1</em>') // *italik* -> <em>
                      .replace(/### (.*?)(\n|$)/g, '<h3 class="text-xl font-semibold mt-6 mb-4 text-gray-900">$1</h3>$2') // ### başlık -> <h3>
                      .replace(/## (.*?)(\n|$)/g, '<h2 class="text-2xl font-semibold mt-8 mb-6 text-gray-900">$1</h2>$2') // ## başlık -> <h2>
                      .replace(/# (.*?)(\n|$)/g, '<h1 class="text-3xl font-bold mt-8 mb-6 text-gray-900">$1</h1>$2') // # başlık -> <h1>
                      .replace(/\n\n/g, '</p><p class="mb-4">') // Çift satır sonu -> paragraf
                      .replace(/^(.)/g, '<p class="mb-4">$1') // Başlangıca <p> ekle
                      .replace(/(.)$/g, '$1</p>') // Sona </p> ekle
                  }}
                />
              ) : (
                <div className="text-center py-12">
                  <div className="text-gray-500">
                    <h3 className="text-xl font-medium mb-4">Makale İçeriği Yükleniyor...</h3>
                    <p>Makale içeriği henüz yüklenemedi. Lütfen daha sonra tekrar deneyin.</p>
                  </div>
                </div>
              )}

              <div className="text-center mt-16">
                <button
                  onClick={() => setSelectedArticle(null)}
                  className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-slate-800 to-slate-900 text-white rounded-lg hover:from-slate-900 hover:to-black transition-all duration-300 shadow-lg hover:shadow-xl"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                  </svg>
                  Diğer Makaleleri Görüntüle
                </button>
              </div>
            </div>
          </div>
        </section>

        <Footer />
      </main>
    );
  }

  return (
    <main className="min-h-screen">
      <Header currentPage="makalelerimiz" />

      {/* Hero Section */}
      <section className="pt-32 pb-16 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto text-center text-white">
            <div className="inline-flex items-center px-4 py-2 bg-white/10 rounded-full text-white text-sm font-medium mb-6 border border-white/20">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
              Araştırma Makalelerimiz
            </div>
            <h1 className="text-4xl md:text-6xl font-light mb-6 text-white">
              Makalelerimiz
            </h1>
            <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
              Meta Global Stratejiler Ekonomik ve Siyasal Araştırmalar Merkezi'nin
              derinlemesine analiz ve araştırma makaleleri
            </p>
          </div>
        </div>
      </section>

      {/* Articles Grid */}
      <section className="py-24 bg-white">
        <div className="container mx-auto px-6">
          <div className="max-w-6xl mx-auto">
            {articles.length === 0 ? (
              <div className="text-center py-16">
                <div className="text-gray-500">
                  <h3 className="text-2xl font-light mb-4">Henüz makale bulunmuyor</h3>
                  <p className="text-lg">Yakında yeni makaleler eklenecektir.</p>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6">
                {articles.map((article) => (
                    <article
                      key={article.id}
                      onClick={() => handleArticleClick(article)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault();
                          handleArticleClick(article);
                        }
                      }}
                      tabIndex={0}
                      role="button"
                      aria-label={`${article.title} makalesini oku`}
                      className="group bg-white rounded-xl shadow-sm hover:shadow-xl transition-all duration-500 hover:-translate-y-2 overflow-hidden border border-gray-100 cursor-pointer focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2"
                    >
                      {/* Header with site color */}
                      <div className="h-1.5 bg-slate-900"></div>

                      <div className="p-4 md:p-6">
                        {/* Category Badge and Date */}
                        <div className="mb-3 md:mb-4 flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                          <div className="inline-flex items-center px-2 md:px-3 py-1 md:py-1.5 bg-slate-50 text-slate-700 text-xs md:text-sm font-medium rounded-full border border-slate-200 w-fit">
                            <span className="w-1.5 md:w-2 h-1.5 md:h-2 bg-slate-600 rounded-full mr-1.5 md:mr-2"></span>
                            <span className="truncate">{article.category}</span>
                          </div>
                          <div className="text-xs text-gray-400 md:text-right">
                            {formatDate(article.published_at)}
                          </div>
                        </div>

                        {/* Title - Full height to fit properly */}
                        <h2 className="text-sm md:text-lg font-semibold text-gray-900 mb-3 md:mb-4 group-hover:text-slate-700 transition-colors duration-300 leading-tight min-h-[3rem] md:min-h-[3.5rem] flex items-start">
                          {article.title}
                        </h2>

                        {/* Author */}
                        <div className="mb-4">
                          <div className="text-xs text-gray-500">
                            <span className="font-medium">{article.author}</span>
                          </div>
                        </div>

                        {/* Read More Button */}
                        <div className="pt-3 md:pt-4 border-t border-gray-100">
                          <div className="flex items-center justify-center md:justify-end text-slate-600 text-xs md:text-sm font-medium group-hover:text-slate-800 transition-colors">
                            <span className="hidden md:inline">Devamını Oku</span>
                            <span className="md:hidden">Oku</span>
                            <svg className="w-3 md:w-4 h-3 md:h-4 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                          </div>
                        </div>
                      </div>
                    </article>
                ))}
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center items-center space-x-4 mt-16">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="px-6 py-3 bg-slate-800 hover:bg-slate-900 text-white rounded-lg transition-all duration-300 font-medium shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  ← Önceki
                </button>

                <span className="text-gray-600 font-medium">
                  Sayfa {currentPage} / {totalPages}
                </span>

                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="px-6 py-3 bg-slate-800 hover:bg-slate-900 text-white rounded-lg transition-all duration-300 font-medium shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Sonraki →
                </button>
              </div>
            )}
          </div>
        </div>
      </section>

      <Footer />
    </main>
  );
}
