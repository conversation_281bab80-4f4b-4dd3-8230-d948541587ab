-- <PERSON><PERSON><PERSON><PERSON><PERSON> tablosu
CREATE TABLE IF NOT EXISTS notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('contact', 'meeting', 'system', 'info') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    related_id INT NULL, -- <PERSON><PERSON><PERSON><PERSON> ka<PERSON>'si (contact_id veya meeting_id)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_is_read (is_read),
    INDEX idx_type (type),
    INDEX idx_created_at (created_at)
);

-- <PERSON><PERSON><PERSON> (development için)
INSERT INTO notifications (title, message, type, related_id) VALUES
('Ye<PERSON>', '<PERSON><PERSON>ı<PERSON> adlı kişiden yeni bir mesaj geldi.', 'contact', 1),
('Toplantı Talebi', 'Can Özdemir adlı kişiden yeni toplantı talebi geldi.', 'meeting', 1),
('Yeni İletişim Mesajı', 'Ayşe Özkan adlı kişiden yeni bir mesaj geldi.', 'contact', 4),
('Toplantı Talebi', 'Emre Yıldız adlı kişiden yeni toplantı talebi geldi.', 'meeting', 3),
('Sistem Bildirimi', 'Sistem bakımı tamamlandı.', 'system', NULL);
