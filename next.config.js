/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'export', // Static export for PHP hosting
  trailingSlash: true,
  poweredByHeader: false,
  compress: true,
  reactStrictMode: true,
  productionBrowserSourceMaps: false,
  // Disable prefetching for static export
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['lucide-react'],
    cssChunking: 'strict',
  },
  images: {
    unoptimized: true,
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
    ],
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    minimumCacheTTL: 31536000, // 1 year
  },
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
  modularizeImports: {
    '@heroicons/react/24/outline': {
      transform: '@heroicons/react/24/outline/{{member}}',
    },
  },
  env: {
    CUSTOM_KEY: 'metaanalizgroup',
  },
  // Headers don't work with static export, will be handled via .htaccess
};

module.exports = nextConfig; 