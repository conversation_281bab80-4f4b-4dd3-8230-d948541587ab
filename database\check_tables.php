<?php
// Veritabanı tablo kontrolü
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Config dosyasını include et
if (file_exists(__DIR__ . '/../api/config.php')) {
    require_once __DIR__ . '/../api/config.php';
} elseif (file_exists('./api/config.php')) {
    require_once './api/config.php';
} elseif (file_exists('../api/config.php')) {
    require_once '../api/config.php';
} else {
    die('Config dosyası bulunamadı!');
}

echo "<h2>Veritabanı Tablo Kontrolü</h2>";

try {
    $pdo = getDBConnection();
    
    // Mevcut tabloları listele
    echo "<h3>Mevcut Tablolar:</h3>";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li><strong>$table</strong></li>";
    }
    echo "</ul>";
    
    // Contact tabloları kontrol et
    echo "<h3>Contact Tabloları Analizi:</h3>";
    
    if (in_array('contact_forms', $tables)) {
        echo "<h4>contact_forms tablosu:</h4>";
        $stmt = $pdo->query("DESCRIBE contact_forms");
        $columns = $stmt->fetchAll();
        echo "<ul>";
        foreach ($columns as $column) {
            echo "<li>{$column['Field']} - {$column['Type']}</li>";
        }
        echo "</ul>";
        
        // Kayıt sayısı
        $stmt = $pdo->query("SELECT COUNT(*) FROM contact_forms");
        $count = $stmt->fetchColumn();
        echo "<p>Toplam kayıt: $count</p>";
    }
    
    if (in_array('contact_messages', $tables)) {
        echo "<h4>contact_messages tablosu:</h4>";
        $stmt = $pdo->query("DESCRIBE contact_messages");
        $columns = $stmt->fetchAll();
        echo "<ul>";
        foreach ($columns as $column) {
            echo "<li>{$column['Field']} - {$column['Type']}</li>";
        }
        echo "</ul>";
        
        // Kayıt sayısı
        $stmt = $pdo->query("SELECT COUNT(*) FROM contact_messages");
        $count = $stmt->fetchColumn();
        echo "<p>Toplam kayıt: $count</p>";
    }
    
    // Meeting tablosu kontrol et
    echo "<h3>Meeting Requests Tablosu:</h3>";
    if (in_array('meeting_requests', $tables)) {
        $stmt = $pdo->query("DESCRIBE meeting_requests");
        $columns = $stmt->fetchAll();
        echo "<ul>";
        foreach ($columns as $column) {
            echo "<li>{$column['Field']} - {$column['Type']}</li>";
        }
        echo "</ul>";
        
        // Kayıt sayısı
        $stmt = $pdo->query("SELECT COUNT(*) FROM meeting_requests");
        $count = $stmt->fetchColumn();
        echo "<p>Toplam kayıt: $count</p>";
    }
    
    // Sites tablosu kontrol et
    echo "<h3>Sites Tablosu:</h3>";
    if (in_array('sites', $tables)) {
        $stmt = $pdo->query("SELECT * FROM sites");
        $sites = $stmt->fetchAll();
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Site Code</th><th>Site Name</th><th>URL</th></tr>";
        foreach ($sites as $site) {
            echo "<tr>";
            echo "<td>{$site['id']}</td>";
            echo "<td>{$site['site_code']}</td>";
            echo "<td>{$site['site_name']}</td>";
            echo "<td>{$site['site_url']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>Sites tablosu bulunamadı!</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Hata: " . $e->getMessage() . "</p>";
}
?>
