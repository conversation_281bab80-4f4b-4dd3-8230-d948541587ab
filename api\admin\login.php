<?php
require_once '../config.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    sendResponse(false, 'Method not allowed');
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    sendResponse(false, 'Invalid JSON input');
}

$username = sanitizeInput($input['username'] ?? '');
$password = $input['password'] ?? '';

if (empty($username) || empty($password)) {
    sendResponse(false, 'Kullanıcı adı ve şifre gerekli');
}

try {
    $pdo = getDBConnection();

    // Get admin user from database
    $stmt = $pdo->prepare("SELECT id, username, email, password_hash, full_name, role, is_active FROM admin_users WHERE username = ? AND is_active = 1");
    $stmt->execute([$username]);
    $user = $stmt->fetch();

    if (!$user) {
        sendResponse(false, 'Geçersiz kullanıcı adı veya şifre');
    }

    // Debug: Log user data and password verification
    error_log('Login attempt - Username: ' . $username);
    error_log('User found in DB: ' . json_encode([
        'id' => $user['id'],
        'username' => $user['username'],
        'is_active' => $user['is_active'],
        'password_hash_length' => strlen($user['password_hash'])
    ]));
    error_log('Input password: ' . $password);
    error_log('Stored password hash: ' . $user['password_hash']);

    // Verify password - check both hashed and plain text for compatibility
    $passwordValid = false;
    if (password_verify($password, $user['password_hash'])) {
        error_log('Password verified with password_verify()');
        $passwordValid = true;
    } elseif ($user['password_hash'] === $password) {
        error_log('Password matched as plain text');
        $passwordValid = true;
    } else {
        error_log('Password verification failed for both hashed and plain text');
    }

    if (!$passwordValid) {
        sendResponse(false, 'Geçersiz kullanıcı adı veya şifre');
    }

    // Create session
    session_start();
    $_SESSION['admin_user'] = [
        'id' => $user['id'],
        'username' => $user['username'],
        'email' => $user['email'],
        'full_name' => $user['full_name'],
        'role' => $user['role']
    ];

    // Update last login
    $stmt = $pdo->prepare("UPDATE admin_users SET last_login = NOW() WHERE id = ?");
    $stmt->execute([$user['id']]);

    sendResponse(true, 'Giriş başarılı', [
        'user' => [
            'id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'fullName' => $user['full_name'],
            'role' => $user['role']
        ]
    ]);

} catch (Exception $e) {
    error_log('Admin login error: ' . $e->getMessage());
    sendResponse(false, 'Giriş yapılırken bir hata oluştu');
}
