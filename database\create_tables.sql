-- Meta Analiz Group Database Tables
-- Veritabanı: metaanalizgroup_mah

-- İletişim formu tablosu
CREATE TABLE IF NOT EXISTS contact_forms (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NULL,
    subject VARCHAR(500) NOT NULL,
    message TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('new', 'read', 'replied', 'closed') DEFAULT 'new',
    INDEX idx_email (email),
    INDEX idx_created_at (created_at),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Toplantı talepleri tablosu
CREATE TABLE IF NOT EXISTS meeting_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NULL,
    company VARCHAR(255) NULL,
    meeting_type ENUM('online', 'office', 'phone') NOT NULL,
    preferred_date DATE NOT NULL,
    preferred_time TIME NOT NULL,
    message TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('pending', 'confirmed', 'completed', 'cancelled') DEFAULT 'pending',
    INDEX idx_email (email),
    INDEX idx_preferred_date (preferred_date),
    INDEX idx_status (status),
    INDEX idx_meeting_type (meeting_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Admin kullanıcıları tablosu (opsiyonel - admin paneli için)
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(100) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    role ENUM('admin', 'manager', 'viewer') DEFAULT 'viewer',
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Site ayarları tablosu (opsiyonel)
CREATE TABLE IF NOT EXISTS site_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NULL,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_setting_key (setting_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Varsayılan admin kullanıcısı ekleme
-- Kullanıcı adı: admin, Şifre: tuberadmin1
-- bcrypt hash for 'tuberadmin1'
INSERT IGNORE INTO admin_users (username, email, password_hash, full_name, role, is_active, created_at) VALUES
('admin', '<EMAIL>', '$2b$12$ppahcVUE./RB3UEv1NiUgO/lq68Xu4c1Rvsp7i1mD6gN0Kcxv/kY.', 'Meta Analiz Admin', 'admin', 1, NOW());

-- Varsayılan site ayarları
INSERT IGNORE INTO site_settings (setting_key, setting_value, setting_type, description) VALUES 
('site_name', 'Meta Analiz Group', 'string', 'Site adı'),
('contact_email', '<EMAIL>', 'string', 'İletişim email adresi'),
('contact_phone', '+90 (542) 797 05 00', 'string', 'İletişim telefon numarası'),
('office_address', 'Yenicami Mah. Özmen Sok. No: 24/A Söke, Aydın', 'string', 'Ofis adresi'),
('business_hours', '{"monday": "09:00-18:00", "tuesday": "09:00-18:00", "wednesday": "09:00-18:00", "thursday": "09:00-18:00", "friday": "09:00-18:00", "saturday": "closed", "sunday": "closed"}', 'json', 'Çalışma saatleri');

-- Veritabanı oluşturma tamamlandı
-- Bu script'i phpMyAdmin veya MySQL komut satırında çalıştırabilirsiniz.
