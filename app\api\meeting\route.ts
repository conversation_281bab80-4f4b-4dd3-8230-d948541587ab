import { NextRequest, NextResponse } from 'next/server';
import { saveMeetingForm } from '@/lib/db';

export const dynamic = 'force-static';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    const { name, email, service, meeting_date, meeting_time } = body;

    if (!name || !email || !service || !meeting_date || !meeting_time) {
      return NextResponse.json(
        { error: 'Gerekli alanlar eksik' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Geçersiz email formatı' },
        { status: 400 }
      );
    }

    // Validate date format (YYYY-MM-DD)
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(meeting_date)) {
      return NextResponse.json(
        { error: 'Geçersiz tarih formatı' },
        { status: 400 }
      );
    }

    // Validate time format (HH:MM)
    const timeRegex = /^\d{2}:\d{2}$/;
    if (!timeRegex.test(meeting_time)) {
      return NextResponse.json(
        { error: 'Geçersiz saat formatı' },
        { status: 400 }
      );
    }

    // Save to database
    await saveMeetingForm({
      name: name.trim(),
      email: email.trim().toLowerCase(),
      phone: body.phone?.trim() || null,
      company: body.company?.trim() || null,
      service: service.trim(),
      meeting_date: meeting_date,
      meeting_time: meeting_time,
      message: body.message?.trim() || null,
    });

    return NextResponse.json(
      { message: 'Toplantı talebiniz başarıyla gönderildi. En kısa sürede size dönüş yapacağız.' },
      { status: 200 }
    );

  } catch (error) {
    console.error('Meeting form error:', error);
    return NextResponse.json(
      { error: 'Toplantı talebi gönderilirken bir hata oluştu. Lütfen tekrar deneyin.' },
      { status: 500 }
    );
  }
}
