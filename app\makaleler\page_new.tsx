'use client';

import Header from '../components/Header';
import Footer from '../components/Footer';
import { useEffect, useState } from 'react';
import Link from 'next/link';
import { Calendar, User, Search, Filter, ChevronLeft, ChevronRight } from 'lucide-react';

interface Article {
  id: number;
  title: string;
  slug: string;
  excerpt: string;
  author: string;
  category: string;
  created_at: string;
  published_at: string;
  site_code: string;
}

interface Category {
  category: string;
  count: number;
}

export default function Makaleler() {
  const [articles, setArticles] = useState<Article[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const articlesPerPage = 6;

  useEffect(() => {
    fetchArticles();
  }, [selectedCategory, searchTerm, currentPage]);

  const fetchArticles = async () => {
    try {
      setLoading(true);
      
      const params = new URLSearchParams({
        site_code: 'mgsam',
        page: currentPage.toString(),
        limit: articlesPerPage.toString(),
        ...(selectedCategory && { category: selectedCategory }),
        ...(searchTerm && { search: searchTerm })
      });
      
      const response = await fetch(`/api/articles.php?${params}`);
      
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          setArticles(data.data.articles || []);
          setCategories(data.data.categories || []);
          setTotalPages(Math.ceil((data.data.total || 0) / articlesPerPage));
        } else {
          setArticles([]);
          setCategories([]);
        }
      } else {
        // Fallback mock data
        setArticles([
          {
            id: 1,
            title: "Afrika'daki Darbe Girişimleri ve İstikrarsızlık Üzerine Analiz Raporu",
            slug: "afrika-darbe-girisimi-analiz",
            excerpt: "Afrika kıtasında yaşanan darbe girişimleri ve siyasi istikrarsızlığın derinlemesine analizi.",
            author: "MGSAM Editör",
            category: "Siyasi Analiz",
            created_at: "2025-01-15 10:00:00",
            published_at: "2025-01-15 14:30:00",
            site_code: "mgsam"
          },
          {
            id: 2,
            title: "Küresel ve Ulusal Enflasyon Dinamikleri",
            slug: "kuresel-ulusal-enflasyon",
            excerpt: "Dünya genelinde yaşanan enflasyonist süreçlerin ekonomik ve sosyal etkileri.",
            author: "MGSAM Editör",
            category: "Ekonomi",
            created_at: "2025-01-10 09:15:00",
            published_at: "2025-01-10 16:45:00",
            site_code: "mgsam"
          }
        ]);
        setCategories([
          { category: "Siyasi Analiz", count: 3 },
          { category: "Ekonomi", count: 2 },
          { category: "Bölgesel Analiz", count: 1 }
        ]);
        setTotalPages(1);
      }
    } catch (error) {
      console.error('Fetch error:', error);
      setArticles([]);
      setCategories([]);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
  };

  // Handle category filter
  const handleCategoryFilter = (category: string) => {
    setSelectedCategory(category === selectedCategory ? '' : category);
    setCurrentPage(1);
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  if (loading) {
    return (
      <main className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700">
        <Header />
        <div className="pt-32 pb-16">
          <div className="container mx-auto px-6">
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto mb-4"></div>
              <p className="text-white">Makaleler yükleniyor...</p>
            </div>
          </div>
        </div>
        <Footer />
      </main>
    );
  }

  return (
    <main className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto text-center text-white">
            <h1 className="text-4xl md:text-6xl font-light mb-6">
              Araştırma <span className="text-green-400">Makalelerimiz</span>
            </h1>
            <p className="text-xl text-slate-300 mb-8 max-w-2xl mx-auto">
              Meta Global Stratejiler Ekonomik ve Siyasal Araştırmalar Merkezi'nin 
              derinlemesine analiz ve araştırma makaleleri
            </p>
          </div>
        </div>
      </section>

      {/* Search and Filter Section */}
      <section className="pb-8">
        <div className="container mx-auto px-6">
          <div className="max-w-6xl mx-auto">
            {/* Search Bar */}
            <form onSubmit={handleSearch} className="mb-8">
              <div className="relative max-w-md mx-auto">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-5 w-5" />
                <input
                  type="text"
                  placeholder="Makale ara..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 bg-slate-800/50 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-green-400 focus:border-transparent"
                />
              </div>
            </form>

            {/* Category Filter */}
            <div className="flex flex-wrap justify-center gap-3 mb-8">
              <button
                onClick={() => handleCategoryFilter('')}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                  selectedCategory === '' 
                    ? 'bg-green-500 text-white shadow-lg' 
                    : 'bg-slate-700/50 text-slate-300 hover:bg-slate-600/50 border border-slate-600'
                }`}
              >
                Tümü
              </button>
              {categories.map((cat) => (
                <button
                  key={cat.category}
                  onClick={() => handleCategoryFilter(cat.category)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                    selectedCategory === cat.category 
                      ? 'bg-green-500 text-white shadow-lg' 
                      : 'bg-slate-700/50 text-slate-300 hover:bg-slate-600/50 border border-slate-600'
                  }`}
                >
                  {cat.category} ({cat.count})
                </button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Articles Grid */}
      <section className="pb-16">
        <div className="container mx-auto px-6">
          <div className="max-w-6xl mx-auto">
            {articles.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-slate-400 mb-4">
                  <Filter className="h-12 w-12 mx-auto mb-4" />
                  <p className="text-lg">Henüz makale bulunmuyor</p>
                  <p className="text-sm">Farklı kategorileri deneyebilirsiniz</p>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {articles.map((article) => (
                  <Link
                    key={article.id}
                    href={`/makale-detay/${article.slug}`}
                    className="group"
                  >
                    <article className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 overflow-hidden h-full">
                      <div className="p-6 h-full flex flex-col">
                        {/* Category Badge */}
                        <div className="inline-flex items-center px-3 py-1 bg-slate-100 text-slate-700 text-xs font-medium rounded-full mb-4 self-start">
                          <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                          {article.category}
                        </div>

                        {/* Title */}
                        <h2 className="text-xl font-semibold text-slate-900 mb-3 group-hover:text-green-600 transition-colors line-clamp-2">
                          {article.title}
                        </h2>

                        {/* Excerpt */}
                        <p className="text-slate-600 mb-4 text-sm leading-relaxed line-clamp-3 flex-grow">
                          {article.excerpt}
                        </p>

                        {/* Meta Info */}
                        <div className="flex items-center justify-between pt-4 border-t border-slate-100">
                          <div className="flex items-center space-x-3 text-xs text-slate-500">
                            <div className="flex items-center">
                              <User className="h-3 w-3 mr-1" />
                              {article.author}
                            </div>
                            <span>•</span>
                            <div className="flex items-center">
                              <Calendar className="h-3 w-3 mr-1" />
                              {formatDate(article.published_at)}
                            </div>
                          </div>
                        </div>
                      </div>
                    </article>
                  </Link>
                ))}
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center items-center space-x-2 mt-12">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="p-2 rounded-lg bg-slate-700/50 text-slate-300 hover:bg-slate-600/50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <ChevronLeft className="h-5 w-5" />
                </button>
                
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <button
                    key={page}
                    onClick={() => handlePageChange(page)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      currentPage === page
                        ? 'bg-green-500 text-white'
                        : 'bg-slate-700/50 text-slate-300 hover:bg-slate-600/50'
                    }`}
                  >
                    {page}
                  </button>
                ))}
                
                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="p-2 rounded-lg bg-slate-700/50 text-slate-300 hover:bg-slate-600/50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <ChevronRight className="h-5 w-5" />
                </button>
              </div>
            )}
          </div>
        </div>
      </section>

      <Footer />
    </main>
  );
}
