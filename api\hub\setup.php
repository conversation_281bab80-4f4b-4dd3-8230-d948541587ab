<?php
// Hub kurulum API'si - Meta Analiz Group sunucusunda çalışacak
require_once '../config.php';

// Güvenlik kontrolü - sadece localhost'tan erişim
$allowedIPs = ['127.0.0.1', '::1', 'localhost'];
$clientIP = $_SERVER['REMOTE_ADDR'] ?? '';

if (!in_array($clientIP, $allowedIPs)) {
    http_response_code(403);
    sendResponse(false, 'Bu API sadece localhost\'tan erişilebilir');
}

// POST request kontrolü
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    sendResponse(false, 'Sadece POST method desteklenir');
}

// JSON input al
$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? '';

try {
    $pdo = getDBConnection();
    
    switch ($action) {
        case 'check_tables':
            checkTables($pdo);
            break;
            
        case 'create_hub_tables':
            createHubTables($pdo);
            break;
            
        case 'add_site_columns':
            addSiteColumns($pdo);
            break;
            
        case 'insert_sites':
            insertSites($pdo);
            break;
            
        case 'full_setup':
            fullSetup($pdo);
            break;
            
        default:
            sendResponse(false, 'Geçersiz action parametresi');
    }
    
} catch (Exception $e) {
    sendResponse(false, 'Hata: ' . $e->getMessage());
}

function checkTables($pdo) {
    $result = [];
    
    // Mevcut tabloları kontrol et
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    $result['existing_tables'] = $tables;
    
    // Gerekli tabloları kontrol et
    $requiredTables = ['contact_forms', 'meeting_requests', 'admin_users'];
    foreach ($requiredTables as $table) {
        $exists = in_array($table, $tables);
        $result['required_tables'][$table] = $exists;
        
        if ($exists) {
            // site_id kolonu var mı kontrol et
            $stmt = $pdo->query("SHOW COLUMNS FROM $table LIKE 'site_id'");
            $result['site_id_columns'][$table] = $stmt->rowCount() > 0;
        }
    }
    
    // sites tablosu kontrol et
    $result['sites_table_exists'] = in_array('sites', $tables);
    
    sendResponse(true, 'Tablo kontrolü tamamlandı', $result);
}

function createHubTables($pdo) {
    // sites tablosu oluştur
    $sql = "
    CREATE TABLE IF NOT EXISTS sites (
        id INT AUTO_INCREMENT PRIMARY KEY,
        site_code VARCHAR(50) NOT NULL UNIQUE,
        site_name VARCHAR(255) NOT NULL,
        site_url VARCHAR(255) NOT NULL,
        site_description TEXT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_site_code (site_code),
        INDEX idx_is_active (is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    
    // notifications tablosu oluştur
    $sql = "
    CREATE TABLE IF NOT EXISTS notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        site_id INT NULL,
        type ENUM('contact', 'meeting', 'callback') NOT NULL,
        reference_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE SET NULL,
        INDEX idx_type (type),
        INDEX idx_is_read (is_read),
        INDEX idx_site_id (site_id),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    
    sendResponse(true, 'Hub tabloları başarıyla oluşturuldu');
}

function addSiteColumns($pdo) {
    try {
        // contact_forms tablosuna site_id ekle
        $pdo->exec("ALTER TABLE contact_forms ADD COLUMN site_id INT NULL AFTER id");
        $pdo->exec("ALTER TABLE contact_forms ADD FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE SET NULL");
    } catch (Exception $e) {
        // Kolon zaten varsa hata vermez
    }
    
    try {
        // meeting_requests tablosuna site_id ekle
        $pdo->exec("ALTER TABLE meeting_requests ADD COLUMN site_id INT NULL AFTER id");
        $pdo->exec("ALTER TABLE meeting_requests ADD FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE SET NULL");
    } catch (Exception $e) {
        // Kolon zaten varsa hata vermez
    }
    
    sendResponse(true, 'Site ID kolonları başarıyla eklendi');
}

function insertSites($pdo) {
    $sql = "INSERT IGNORE INTO sites (site_code, site_name, site_url, site_description, is_active) VALUES 
            ('metaanaliz-group', 'Meta Analiz Group', 'https://metaanalizgroup.com', 'Ana grup şirketi web sitesi', 1),
            ('metaanaliz-musavirlik', 'Meta Analiz Müşavirlik', 'https://metaanalizmusavirlik.com', 'Müşavirlik hizmetleri web sitesi', 1)";
    
    $pdo->exec($sql);
    
    sendResponse(true, 'Site kayıtları başarıyla eklendi');
}

function fullSetup($pdo) {
    try {
        createHubTables($pdo);
        addSiteColumns($pdo);
        insertSites($pdo);
        
        sendResponse(true, 'Hub kurulumu başarıyla tamamlandı');
    } catch (Exception $e) {
        sendResponse(false, 'Kurulum hatası: ' . $e->getMessage());
    }
}
?>
