'use client';

import Link from "next/link";
import Image from "next/image";
import Header from './components/Header';
import Footer from './components/Footer';
import { useEffect, useState } from 'react';

export default function Home() {
  // Hero background images state
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const heroImages = [
    "https://images.unsplash.com/photo-1451187580459-43490279c0fa?q=80&w=2072&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    "/dunya1.jpg",
    "/dunya2.jpg",
    "/dunya3.jpg",
    "/dunya4.jpg"
  ];

  useEffect(() => {
    // Intersection Observer for fade animations
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('visible');
        }
      });
    }, { threshold: 0.1 });

    // Observe all elements with fade animations
    document.querySelectorAll('.fade-up, .fade-in').forEach((el) => {
      observer.observe(el);
    });

    return () => observer.disconnect();
  }, []);

  // Auto-slide effect for hero images
  useEffect(() => {
    const slideInterval = setInterval(() => {
      setCurrentImageIndex((prevIndex) =>
        (prevIndex + 1) % heroImages.length
      );
    }, 5000); // 5 saniyede bir değişir

    return () => clearInterval(slideInterval);
  }, [heroImages.length]);

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Meta Global Stratejiler Ekonomik ve Siyasal Araştırmalar Merkezi",
    "description": "Türkiye'den tüm dünyaya ekonomik ve siyasal araştırmalar merkezi. Yaşamın Kalitesi, Yönetimin Kalitesi ile Artar.",
    "url": "https://mgsam.com",
    "logo": "https://mgsam.com/mgsam_logo.webp",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "Yenicami Mah. Özmen Sok. No: 24/A",
      "addressLocality": "Söke",
      "addressRegion": "Aydın",
      "postalCode": "09270",
      "addressCountry": "TR"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+90-542-797-05-00",
      "contactType": "customer service",
      "email": "<EMAIL>"
    },
    "sameAs": [
      "https://www.facebook.com/@mgsam",
      "https://www.instagram.com/mgsam.official/"
    ],
    "foundingDate": "2020",
    "numberOfEmployees": "25+",
    "areaServed": "Global"
  };

  return (
    <main className="min-h-screen">
      <style dangerouslySetInnerHTML={{
        __html: `
          .hero-section {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
          }
          .fade-up {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s ease-out;
          }
          .fade-up.visible {
            opacity: 1;
            transform: translateY(0);
          }
          .glass-btn {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
          }
          .glass-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
          }
          .hero-image {
            transition: opacity 1.5s ease-in-out;
          }
          .hero-image.fade-out {
            opacity: 0;
          }
          .hero-image.fade-in {
            opacity: 1;
          }
          @keyframes fadeUp {
            from {
              opacity: 0;
              transform: translateY(30px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }
        `
      }} />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <Header currentPage="home" />

      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden pt-48 sm:pt-36 md:pt-20 lg:pt-0">
        {/* Professional Background Images with Slide Effect */}
        <div className="absolute inset-0 z-0">
          {heroImages.map((imageSrc, index) => (
            <Image
              key={`hero-image-${imageSrc.split('/').pop()}-${index}`}
              src={imageSrc}
              alt={`Global Perspective ${index + 1}`}
              fill
              className={`object-cover hero-image ${
                index === currentImageIndex ? 'fade-in' : 'fade-out'
              }`}
              priority={index === 0}
              quality={85}
              sizes="100vw"
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                opacity: index === currentImageIndex ? 1 : 0,
                zIndex: index === currentImageIndex ? 1 : 0
              }}
            />
          ))}
        </div>

        {/* Dark Overlay for Text Readability */}
        <div className="absolute inset-0 bg-gradient-to-r from-slate-900/85 via-slate-800/75 to-slate-700/65 z-10" />

        {/* Additional Professional Elements */}
        <div className="absolute top-0 right-0 w-1/3 h-full bg-gradient-to-l from-slate-900/40 to-transparent z-15"></div>
        <div className="absolute bottom-0 left-0 w-1/2 h-1/3 bg-gradient-to-tr from-slate-900/50 to-transparent z-15"></div>

        <div className="container mx-auto px-6 sm:px-8 relative z-20 py-12 sm:py-8 md:py-4">
          <div className="flex flex-col lg:flex-row justify-between items-center relative gap-8 lg:gap-12">
            {/* Main content */}
            <div className="flex-1 max-w-3xl text-center lg:text-left">
              <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-light text-white mb-8 fade-up leading-tight">
                Meta Global Stratejiler<br />
                <span className="md:whitespace-nowrap">Ekonomik ve Siyasal Araştırmalar Merkezi</span>
              </h1>
              <p className="text-lg sm:text-xl md:text-xl text-white/90 max-w-2xl mb-10 fade-up leading-relaxed mx-auto lg:mx-0" style={{ transitionDelay: '0.2s' }}>
                Kamu kurum ve kuruluşlarının karar alma süreçlerine bilimsel bilgi ve stratejik analizlerle destek oluyoruz. Doğru bilgi, iyi yönetim, sürdürülebilir kalkınma.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center lg:justify-start">
                <Link
                  href="/iletisim"
                  className="glass-btn inline-flex items-center justify-center px-6 py-3 sm:px-8 sm:py-4 md:px-8 text-base sm:text-lg md:text-lg font-medium text-white hover:scale-105 transition-all duration-300"
                >
                  <span>
                    İletişim
                  </span>
                  <svg className="w-5 h-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </Link>
                <Link
                  href="/makaleler"
                  className="glass-btn inline-flex items-center justify-center px-6 py-3 sm:px-8 sm:py-4 md:px-8 text-base sm:text-lg md:text-lg font-medium text-white hover:scale-105 transition-all duration-300"
                >
                  <span>
                    Makalelerimiz
                  </span>
                  <svg className="w-5 h-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>



      {/* Geri Arama Talebi Section */}
      <section className="py-16 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 relative overflow-hidden">
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=%2240%22 height=%2240%22 viewBox=%220 0 40 40%22 xmlns=%22http://www.w3.org/2000/svg%22%3E%3Cg fill=%22none%22 fill-rule=%22evenodd%22%3E%3Cg fill=%22%23ffffff%22 fill-opacity=%220.03%22%3E%3Cpath d=%22m0 40l40-40h-40z%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-30"></div>
        <div className="absolute top-0 right-0 w-1/3 h-full bg-gradient-to-l from-slate-600/20 to-transparent"></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-8">
              <div className="inline-flex items-center bg-slate-700/30 backdrop-blur-sm rounded-full px-4 py-2 mb-4 border border-slate-600/30">
                <span className="w-2 h-2 bg-gradient-to-r from-green-400 to-green-300 rounded-full mr-2"></span>
                <span className="text-white font-medium">Sizi Arayalım</span>
              </div>
              <h2 className="text-3xl md:text-4xl font-extralight text-white mb-3 tracking-tight">
                Geri Arama Talep Edin
              </h2>
              <p className="text-lg text-slate-300 max-w-2xl mx-auto font-light">
                Bilgilerinizi bırakın, uzmanlarımız en kısa sürede sizinle iletişime geçsin
              </p>
            </div>

            <form id="callbackForm">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    required
                    className="w-full px-4 py-3 bg-white border border-gray-300 rounded-2xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent shadow-sm"
                    placeholder="Adınız Soyadınız"
                  />
                </div>
                <div>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    required
                    className="w-full px-4 py-3 bg-white border border-gray-300 rounded-2xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent shadow-sm"
                    placeholder="Telefon Numaranız"
                  />
                </div>
                <div>
                  <select
                    id="preferredTime"
                    name="preferredTime"
                    defaultValue=""
                    className="w-full px-4 py-3 bg-white border border-gray-300 rounded-2xl text-gray-900 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent shadow-sm"
                  >
                    <option value="" disabled className="text-gray-500">Tercih ettiğiniz zaman dilimi</option>
                    <option value="morning" className="text-gray-900">Sabah (09:00-12:00)</option>
                    <option value="afternoon" className="text-gray-900">Öğleden Sonra (13:00-17:00)</option>
                    <option value="evening" className="text-gray-900">Akşam (18:00-20:00)</option>
                  </select>
                </div>
              </div>

              <div className="flex justify-center">
                <button
                  type="submit"
                  className="glass-btn inline-flex items-center px-8 py-3 text-lg font-medium text-white hover:scale-105 transition-all duration-300"
                >
                  <span>
                    Sizi Arayalım
                  </span>
                  <svg className="w-5 h-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </button>
              </div>
            </form>
          </div>
        </div>
      </section>

      {/* Profesyonel Çözümlerimiz Section */}
      <section className="py-24 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-slate-100 rounded-full text-slate-700 text-sm font-medium mb-6">
              <span className="w-2 h-2 bg-gradient-to-r from-green-500 to-green-400 rounded-full mr-3"></span>
              {' '}Hizmet Alanlarımız
            </div>
            <h2 className="text-4xl md:text-5xl font-light mb-4">
              Araştırma <span className="text-primary">{' '}Alanlarımız</span>
            </h2>
            <p className="text-gray-700 text-lg leading-relaxed">
              Ekonomik ve siyasal araştırmalarımızla geleceği şekillendiriyoruz
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 lg:gap-8 max-w-6xl mx-auto">
            <div className="bg-white rounded-xl p-4 md:p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="w-12 h-12 md:w-14 md:h-14 lg:w-16 lg:h-16 bg-primary/10 rounded-xl flex items-center justify-center text-primary mb-4 md:mb-6">
                <svg className="w-6 h-6 md:w-7 md:h-7 lg:w-8 lg:h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h3 className="text-sm md:text-lg lg:text-xl font-semibold text-gray-900 mb-2 md:mb-4">Ekonomik Araştırmalar</h3>
              <p className="text-gray-700 mb-4 md:mb-6 text-xs md:text-sm lg:text-base">Küresel ve ulusal ekonomik trendleri analiz ederek stratejik öngörüler sunuyoruz.</p>
            </div>

            <div className="bg-white rounded-xl p-4 md:p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="w-12 h-12 md:w-14 md:h-14 lg:w-16 lg:h-16 bg-primary/10 rounded-xl flex items-center justify-center text-primary mb-4 md:mb-6">
                <svg className="w-6 h-6 md:w-7 md:h-7 lg:w-8 lg:h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 0 1 9-9" />
                </svg>
              </div>
              <h3 className="text-sm md:text-lg lg:text-xl font-semibold text-gray-900 mb-2 md:mb-4">Siyasal Araştırmalar</h3>
              <p className="text-gray-700 mb-4 md:mb-6 text-xs md:text-sm lg:text-base">Uluslararası siyasi gelişmeleri ve jeopolitik değişimleri derinlemesine analiz ediyoruz.</p>
            </div>

            <div className="bg-white rounded-xl p-4 md:p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="w-12 h-12 md:w-14 md:h-14 lg:w-16 lg:h-16 bg-primary/10 rounded-xl flex items-center justify-center text-primary mb-4 md:mb-6">
                <svg className="w-6 h-6 md:w-7 md:h-7 lg:w-8 lg:h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-sm md:text-lg lg:text-xl font-semibold text-gray-900 mb-2 md:mb-4">Kriz Analizi</h3>
              <p className="text-gray-700 mb-4 md:mb-6 text-xs md:text-sm lg:text-base">Küresel krizlerin etkilerini değerlendirip çözüm önerileri geliştiriyoruz.</p>
            </div>

            <div className="bg-white rounded-xl p-4 md:p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="w-12 h-12 md:w-14 md:h-14 lg:w-16 lg:h-16 bg-primary/10 rounded-xl flex items-center justify-center text-primary mb-4 md:mb-6">
                <svg className="w-6 h-6 md:w-7 md:h-7 lg:w-8 lg:h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-sm md:text-lg lg:text-xl font-semibold text-gray-900 mb-2 md:mb-4">Bölgesel Çalışmalar</h3>
              <p className="text-gray-700 mb-4 md:mb-6 text-xs md:text-sm lg:text-base">Ortadoğu, Afrika ve Asya bölgelerindeki gelişmeleri yakından takip ediyoruz.</p>
            </div>

            <div className="bg-white rounded-xl p-4 md:p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="w-12 h-12 md:w-14 md:h-14 lg:w-16 lg:h-16 bg-primary/10 rounded-xl flex items-center justify-center text-primary mb-4 md:mb-6">
                <svg className="w-6 h-6 md:w-7 md:h-7 lg:w-8 lg:h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                </svg>
              </div>
              <h3 className="text-sm md:text-lg lg:text-xl font-semibold text-gray-900 mb-2 md:mb-4">Stratejik Raporlama</h3>
              <p className="text-gray-700 mb-4 md:mb-6 text-xs md:text-sm lg:text-base">Detaylı analiz raporları ile karar vericilere rehberlik ediyoruz.</p>
            </div>

            <div className="bg-white rounded-xl p-4 md:p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="w-12 h-12 md:w-14 md:h-14 lg:w-16 lg:h-16 bg-primary/10 rounded-xl flex items-center justify-center text-primary mb-4 md:mb-6">
                <svg className="w-6 h-6 md:w-7 md:h-7 lg:w-8 lg:h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
              </div>
              <h3 className="text-sm md:text-lg lg:text-xl font-semibold text-gray-900 mb-2 md:mb-4">Sürdürülebilirlik Araştırmaları</h3>
              <p className="text-gray-700 mb-4 md:mb-6 text-xs md:text-sm lg:text-base">İklim değişikliği ve çevre politikalarının ekonomik etkilerini inceliyoruz.</p>
            </div>

          </div>
        </div>
      </section>

      <script dangerouslySetInnerHTML={{
        __html: `
          document.addEventListener('DOMContentLoaded', function() {
            const callbackForm = document.getElementById('callbackForm');
            if (callbackForm) {
              callbackForm.addEventListener('submit', async function(e) {
                e.preventDefault();

                const submitBtn = this.querySelector('button[type="submit"]');
                const originalHTML = submitBtn.innerHTML;

                // Show loading state
                submitBtn.disabled = true;
                submitBtn.innerHTML = 'Gönderiliyor...';

                try {
                  const formData = new FormData(this);
                  const fullName = formData.get('name').trim();
                  const nameParts = fullName.split(' ');
                  const name = nameParts[0] || '';
                  const surname = nameParts.slice(1).join(' ') || '';

                  const data = {
                    name: name,
                    surname: surname,
                    phone: formData.get('phone'),
                    preferredTime: formData.get('preferredTime'),
                    message: formData.get('message')
                  };

                  const response = await fetch('/api/callback.php', {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                  });

                  const result = await response.json();

                  if (result.success) {
                    // Success message
                    submitBtn.innerHTML = '✓ Gönderildi';
                    submitBtn.className = submitBtn.className.replace('glass-btn', 'glass-btn-success');

                    // Reset form
                    this.reset();

                    // Show success alert
                    alert('Geri arama talebiniz başarıyla alındı! En kısa sürede sizinle iletişime geçeceğiz.');

                    // Reset button after 3 seconds
                    setTimeout(() => {
                      submitBtn.disabled = false;
                      submitBtn.innerHTML = originalHTML;
                      submitBtn.className = submitBtn.className.replace('glass-btn-success', 'glass-btn');
                    }, 3000);
                  } else {
                    throw new Error(result.message || 'Bir hata oluştu');
                  }
                } catch (error) {
                  console.error('Callback request error:', error);
                  alert('Hata: ' + error.message);

                  // Reset button
                  submitBtn.disabled = false;
                  submitBtn.innerHTML = originalHTML;
                }
              });
            }
          });
        `
      }} />

      <Footer />
    </main>
  );
}