'use client';

import { useState } from 'react';
import Header from "../components/Header";
import Footer from "../components/Footer";

export default function ToplantiPlanla() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    service: '',
    date: '',
    time: '',
    message: '',
    notes: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState('');
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitMessage('');

    try {
      const response = await fetch('/api/meeting.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          meeting_date: formData.date,
          meeting_time: formData.time
        }),
      });

      const result = await response.json();

      if (result.success) {
        setSubmitSuccess(true);
        setSubmitMessage(result.message);
        setFormData({
          name: '',
          email: '',
          phone: '',
          company: '',
          service: '',
          date: '',
          time: '',
          message: '',
          notes: ''
        });
      } else {
        setSubmitSuccess(false);
        setSubmitMessage(result.message);
      }
    } catch (error) {
      console.error('Form submission error:', error);
      setSubmitSuccess(false);
      setSubmitMessage('Bir hata oluştu. Lütfen daha sonra tekrar deneyin.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const hizmetler = [
    'Stratejik Danışmanlık',
    'İş Geliştirme',
    'Dijital Dönüşüm',
    'Proje Yönetimi',
    'Eğitim ve Gelişim',
    'Diğer'
  ];



  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-white flex flex-col">
      <Header currentPage="toplanti-planla" />

      {/* Main Content */}
      <main className="flex-1 pt-20 pb-8">
        <div className="container mx-auto px-4">
          <div className="max-w-7xl mx-auto flex flex-col">

            {/* Compact Header - Hidden on mobile */}
            <div className="text-center py-8 hidden md:block">
              <div className="inline-flex items-center px-3 py-1 bg-slate-100 rounded-full text-slate-700 text-sm font-medium mb-3">
                <span className="w-2 h-2 bg-gradient-to-r from-green-500 to-green-400 rounded-full mr-2"></span>
                {' '}Ücretsiz Danışmanlık Toplantısı
              </div>
              <h1 className="text-3xl md:text-4xl font-extralight text-slate-900 mb-2 tracking-tight">
                Toplantı{' '}
                <span className="text-slate-700 font-light">Planlayın</span>
              </h1>
              <p className="text-slate-600 font-light text-sm">
                Uzman ekibimizle randevu alın ve projelerinizi değerlendirelim
              </p>
            </div>

            {/* Main Content Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 py-8">

              {/* Randevu Formu - Mobile First */}
              <div className="lg:col-span-2 lg:order-2">
                <div className="bg-white rounded-3xl p-6 shadow-lg border border-slate-200/50 relative overflow-hidden">
                  <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-slate-100/30 to-transparent rounded-full -translate-y-16 translate-x-16"></div>

                  <div className="relative z-10">
                    <div className="mb-6">
                      <h2 className="text-2xl font-light text-slate-900 mb-2 tracking-tight">
                        Randevu Formu
                      </h2>
                      <p className="text-slate-600 text-sm">
                        Detaylı bilgilerinizi paylaşın, size en uygun zamanı belirleyelim
                      </p>
                    </div>

                    <form onSubmit={handleSubmit} className="flex flex-col space-y-4">

                      {/* Kişisel Bilgiler */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div>
                          <input
                            type="text"
                            name="name"
                            placeholder="Adınız Soyadınız *"
                            value={formData.name}
                            onChange={handleChange}
                            required
                            className="w-full px-4 py-2.5 border border-slate-200 rounded-xl focus:ring-2 focus:ring-slate-400 focus:border-transparent transition-all duration-200 text-sm"
                          />
                        </div>
                        <div>
                          <input
                            type="email"
                            name="email"
                            placeholder="E-posta Adresiniz *"
                            value={formData.email}
                            onChange={handleChange}
                            required
                            className="w-full px-4 py-2.5 border border-slate-200 rounded-xl focus:ring-2 focus:ring-slate-400 focus:border-transparent transition-all duration-200 text-sm"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div>
                          <input
                            type="tel"
                            name="phone"
                            placeholder="Telefon Numaranız *"
                            value={formData.phone}
                            onChange={handleChange}
                            required
                            className="w-full px-4 py-2.5 border border-slate-200 rounded-xl focus:ring-2 focus:ring-slate-400 focus:border-transparent transition-all duration-200 text-sm"
                          />
                        </div>
                        <div>
                          <input
                            type="text"
                            name="company"
                            placeholder="Şirket Adınız *"
                            value={formData.company}
                            onChange={handleChange}
                            required
                            className="w-full px-4 py-2.5 border border-slate-200 rounded-xl focus:ring-2 focus:ring-slate-400 focus:border-transparent transition-all duration-200 text-sm"
                          />
                        </div>
                      </div>

                      {/* Hizmet Seçimi */}
                      <div>
                        <select
                          name="service"
                          value={formData.service}
                          onChange={handleChange}
                          required
                          className="w-full px-4 py-2.5 border border-slate-200 rounded-xl focus:ring-2 focus:ring-slate-400 focus:border-transparent transition-all duration-200 text-sm"
                        >
                          <option value="">Hizmet Alanı Seçiniz *</option>
                          {hizmetler.map((hizmet, index) => (
                            <option key={`hizmet-${hizmet.slice(0, 10)}-${index}`} value={hizmet}>
                              {hizmet}
                            </option>
                          ))}
                        </select>
                      </div>

                      {/* Tarih ve Saat */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div>
                          <input
                            type="date"
                            name="date"
                            value={formData.date}
                            onChange={handleChange}
                            required
                            min={new Date().toISOString().split('T')[0]}
                            className="w-full px-4 py-2.5 border border-slate-200 rounded-xl focus:ring-2 focus:ring-slate-400 focus:border-transparent transition-all duration-200 text-sm"
                          />
                        </div>
                        <div>
                          <input
                            type="time"
                            name="time"
                            value={formData.time}
                            onChange={handleChange}
                            required
                            className="w-full px-4 py-2.5 border border-slate-200 rounded-xl focus:ring-2 focus:ring-slate-400 focus:border-transparent transition-all duration-200 text-sm"
                          />
                        </div>
                      </div>

                      {/* Mesaj */}
                      <div>
                        <textarea
                          name="message"
                          placeholder="Toplantıda konuşmak istediğiniz konular *"
                          value={formData.message}
                          onChange={handleChange}
                          required
                          rows={4}
                          className="w-full px-4 py-2.5 border border-slate-200 rounded-xl focus:ring-2 focus:ring-slate-400 focus:border-transparent transition-all duration-200 text-sm resize-none"
                        ></textarea>
                      </div>

                      {/* Submit Message */}
                      {submitMessage && (
                        <div className={`p-3 rounded-xl text-sm font-medium ${
                          submitSuccess
                            ? 'bg-green-50 text-green-700 border border-green-200'
                            : 'bg-red-50 text-red-700 border border-red-200'
                        }`}>
                          {submitMessage}
                        </div>
                      )}

                      {/* Gönder Butonu */}
                      <button
                        type="submit"
                        disabled={isSubmitting}
                        className={`w-full py-2.5 px-6 rounded-xl font-medium transition-all duration-300 transform shadow-lg flex items-center justify-center text-sm ${
                          isSubmitting
                            ? 'bg-slate-400 text-white cursor-not-allowed'
                            : 'bg-gradient-to-r from-slate-900 to-slate-700 text-white hover:from-slate-800 hover:to-slate-600 hover:-translate-y-1 hover:shadow-xl'
                        }`}
                      >
                        <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        {isSubmitting ? 'Gönderiliyor...' : 'Toplantı Talebini Gönder'}
                      </button>
                    </form>
                  </div>
                </div>
              </div>

              {/* Sol Panel - Bilgiler */}
              <div className="lg:col-span-1 lg:order-1 space-y-4">
                
                {/* Toplantı Avantajları */}
                <div className="bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 rounded-3xl p-5 text-white relative overflow-hidden">
                  <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=%2240%22 height=%2240%22 viewBox=%220 0 40 40%22 xmlns=%22http://www.w3.org/2000/svg%22%3E%3Cg fill=%22none%22 fill-rule=%22evenodd%22%3E%3Cg fill=%22%23ffffff%22 fill-opacity=%220.03%22%3E%3Cpath d=%22m0 40l40-40h-40z%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-30"></div>
                  
                  <div className="relative z-10">
                    <h3 className="font-medium text-white mb-3 flex items-center text-sm">
                      <svg className="w-5 h-5 text-slate-200 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                      </svg>
                      Neler Konuşacağız?
                    </h3>
                    <ul className="space-y-2 text-sm text-slate-300">
                      <li className="flex items-start">
                        <div className="w-1.5 h-1.5 bg-slate-300 rounded-full mr-3 mt-1.5 flex-shrink-0"></div>
                        İhtiyaçlarınızı analiz edeceğiz
                      </li>
                      <li className="flex items-start">
                        <div className="w-1.5 h-1.5 bg-slate-300 rounded-full mr-3 mt-1.5 flex-shrink-0"></div>
                        Özel çözüm önerilerimizi paylaşacağız
                      </li>
                      <li className="flex items-start">
                        <div className="w-1.5 h-1.5 bg-slate-300 rounded-full mr-3 mt-1.5 flex-shrink-0"></div>
                        Proje sürecini planlayacağız
                      </li>
                      <li className="flex items-start">
                        <div className="w-1.5 h-1.5 bg-slate-300 rounded-full mr-3 mt-1.5 flex-shrink-0"></div>
                        Sorularınızı yanıtlayacağız
                      </li>
                    </ul>
                  </div>
                </div>

                {/* İletişim Bilgileri */}
                <div className="bg-white rounded-3xl p-5 shadow-lg border border-slate-200/50">
                  <h3 className="font-medium text-slate-900 mb-3 text-sm">Hızlı İletişim</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center">
                      <svg className="w-4 h-4 text-slate-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                      </svg>
                      +90 (542) 797 05 00
                    </div>
                    <div className="flex items-center">
                      <svg className="w-4 h-4 text-slate-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                      <EMAIL>
                    </div>
                  </div>
                </div>

              </div>


            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
