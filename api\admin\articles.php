<?php
// Articles API - CRUD operations for Meta Analiz M<PERSON>şavirlik articles
session_start();

// Admin authentication check
if (!isset($_SESSION['admin_user'])) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access'
    ]);
    exit;
}

require_once __DIR__ . '/../config.php';

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Get article ID from URL if present
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$pathParts = explode('/', trim($path, '/'));
$articleId = null;

// Extract article ID from URL like /api/admin/articles/123
if (count($pathParts) >= 4 && $pathParts[3] === 'articles' && isset($pathParts[4]) && is_numeric($pathParts[4])) {
    $articleId = (int)$pathParts[4];
}

try {
    $pdo = getDBConnection();
    
    switch ($method) {
        case 'GET':
            handleGet($pdo, $articleId);
            break;
            
        case 'POST':
            handlePost($pdo);
            break;
            
        case 'PUT':
            handlePut($pdo, $articleId);
            break;
            
        case 'DELETE':
            handleDelete($pdo, $articleId);
            break;
            
        default:
            http_response_code(405);
            echo json_encode([
                'success' => false,
                'message' => 'Method not allowed'
            ]);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage()
    ]);
}

// GET - List articles or get single article
function handleGet($pdo, $articleId = null) {
    try {
        if ($articleId) {
            // Get single article
            $stmt = $pdo->prepare("
                SELECT a.*, s.site_name, s.site_code 
                FROM articles a 
                LEFT JOIN sites s ON a.site_id = s.id 
                WHERE a.id = ?
            ");
            $stmt->execute([$articleId]);
            $article = $stmt->fetch();
            
            if (!$article) {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => 'Article not found'
                ]);
                return;
            }
            
            echo json_encode([
                'success' => true,
                'article' => $article
            ]);
            
        } else {
            // Get all articles with pagination and filtering
            $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
            $limit = isset($_GET['limit']) ? min(100, max(1, (int)$_GET['limit'])) : 20;
            $offset = ($page - 1) * $limit;
            
            $status = isset($_GET['status']) ? sanitizeInput($_GET['status']) : '';
            $search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
            
            // Build WHERE clause
            $whereConditions = ['a.site_id = 2']; // Meta Analiz Müşavirlik site_id
            $params = [];
            
            if ($status && in_array($status, ['draft', 'published', 'archived'])) {
                $whereConditions[] = 'a.status = ?';
                $params[] = $status;
            }
            
            if ($search) {
                $whereConditions[] = '(a.title LIKE ? OR a.excerpt LIKE ? OR a.content LIKE ?)';
                $searchTerm = '%' . $search . '%';
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }
            
            $whereClause = implode(' AND ', $whereConditions);
            
            // Get total count
            $countStmt = $pdo->prepare("
                SELECT COUNT(*) as total 
                FROM articles a 
                WHERE $whereClause
            ");
            $countStmt->execute($params);
            $total = $countStmt->fetch()['total'];
            
            // Get articles
            $stmt = $pdo->prepare("
                SELECT a.*, s.site_name, s.site_code 
                FROM articles a 
                LEFT JOIN sites s ON a.site_id = s.id 
                WHERE $whereClause 
                ORDER BY a.created_at DESC 
                LIMIT ? OFFSET ?
            ");
            
            $params[] = $limit;
            $params[] = $offset;
            $stmt->execute($params);
            $articles = $stmt->fetchAll();
            
            echo json_encode([
                'success' => true,
                'articles' => $articles,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $total,
                    'pages' => ceil($total / $limit)
                ]
            ]);
        }
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Database error: ' . $e->getMessage()
        ]);
    }
}

// POST - Create new article
function handlePost($pdo) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'Invalid JSON input'
            ]);
            return;
        }
        
        // Validate required fields
        if (empty($input['title']) || empty($input['content'])) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'Title and content are required'
            ]);
            return;
        }
        
        // Generate slug if not provided
        if (empty($input['slug'])) {
            $input['slug'] = generateSlug($input['title']);
        }
        
        // Check if slug already exists
        $slugCheck = $pdo->prepare("SELECT id FROM articles WHERE slug = ? AND site_id = 2");
        $slugCheck->execute([$input['slug']]);
        if ($slugCheck->fetch()) {
            $input['slug'] = $input['slug'] . '-' . time();
        }
        
        // Set published_at if status is published
        $publishedAt = null;
        if (isset($input['status']) && $input['status'] === 'published') {
            $publishedAt = date('Y-m-d H:i:s');
        }
        
        $stmt = $pdo->prepare("
            INSERT INTO articles (
                title, slug, excerpt, content, author, category, status, 
                is_featured, published_at, site_id, meta_title, meta_description, meta_keywords
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 2, ?, ?, ?)
        ");
        
        $result = $stmt->execute([
            sanitizeInput($input['title']),
            sanitizeInput($input['slug']),
            sanitizeInput($input['excerpt'] ?? ''),
            $input['content'], // Don't sanitize HTML content
            sanitizeInput($input['author'] ?? 'Meta Analiz Müşavirlik'),
            sanitizeInput($input['category'] ?? 'Makalelerimiz'),
            sanitizeInput($input['status'] ?? 'draft'),
            isset($input['is_featured']) ? (bool)$input['is_featured'] : false,
            $publishedAt,
            sanitizeInput($input['meta_title'] ?? ''),
            sanitizeInput($input['meta_description'] ?? ''),
            sanitizeInput($input['meta_keywords'] ?? '')
        ]);
        
        if ($result) {
            $articleId = $pdo->lastInsertId();
            
            echo json_encode([
                'success' => true,
                'message' => 'Article created successfully',
                'article_id' => $articleId
            ]);
        } else {
            throw new Exception('Failed to create article');
        }
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Database error: ' . $e->getMessage()
        ]);
    }
}

// PUT - Update article
function handlePut($pdo, $articleId) {
    if (!$articleId) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Article ID is required'
        ]);
        return;
    }
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'Invalid JSON input'
            ]);
            return;
        }
        
        // Check if article exists and belongs to Meta Analiz Müşavirlik
        $checkStmt = $pdo->prepare("SELECT id FROM articles WHERE id = ? AND site_id = 2");
        $checkStmt->execute([$articleId]);
        if (!$checkStmt->fetch()) {
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => 'Article not found'
            ]);
            return;
        }
        
        // Validate required fields
        if (empty($input['title']) || empty($input['content'])) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'Title and content are required'
            ]);
            return;
        }
        
        // Check if slug already exists (excluding current article)
        if (!empty($input['slug'])) {
            $slugCheck = $pdo->prepare("SELECT id FROM articles WHERE slug = ? AND site_id = 2 AND id != ?");
            $slugCheck->execute([$input['slug'], $articleId]);
            if ($slugCheck->fetch()) {
                $input['slug'] = $input['slug'] . '-' . time();
            }
        }
        
        // Set published_at if status is published and not already set
        $publishedAt = null;
        if (isset($input['status']) && $input['status'] === 'published') {
            // Check if already published
            $pubCheck = $pdo->prepare("SELECT published_at FROM articles WHERE id = ?");
            $pubCheck->execute([$articleId]);
            $current = $pubCheck->fetch();
            $publishedAt = $current['published_at'] ?: date('Y-m-d H:i:s');
        }
        
        $stmt = $pdo->prepare("
            UPDATE articles SET 
                title = ?, slug = ?, excerpt = ?, content = ?, author = ?, 
                category = ?, status = ?, is_featured = ?, published_at = ?, 
                meta_title = ?, meta_description = ?, meta_keywords = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ? AND site_id = 2
        ");
        
        $result = $stmt->execute([
            sanitizeInput($input['title']),
            sanitizeInput($input['slug']),
            sanitizeInput($input['excerpt'] ?? ''),
            $input['content'], // Don't sanitize HTML content
            sanitizeInput($input['author'] ?? 'Meta Analiz Müşavirlik'),
            sanitizeInput($input['category'] ?? 'Makalelerimiz'),
            sanitizeInput($input['status'] ?? 'draft'),
            isset($input['is_featured']) ? (bool)$input['is_featured'] : false,
            $publishedAt,
            sanitizeInput($input['meta_title'] ?? ''),
            sanitizeInput($input['meta_description'] ?? ''),
            sanitizeInput($input['meta_keywords'] ?? ''),
            $articleId
        ]);
        
        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => 'Article updated successfully'
            ]);
        } else {
            throw new Exception('Failed to update article');
        }
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Database error: ' . $e->getMessage()
        ]);
    }
}

// DELETE - Delete article
function handleDelete($pdo, $articleId) {
    if (!$articleId) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Article ID is required'
        ]);
        return;
    }
    
    try {
        // Check if article exists and belongs to Meta Analiz Müşavirlik
        $checkStmt = $pdo->prepare("SELECT id, title FROM articles WHERE id = ? AND site_id = 2");
        $checkStmt->execute([$articleId]);
        $article = $checkStmt->fetch();
        
        if (!$article) {
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => 'Article not found'
            ]);
            return;
        }
        
        $stmt = $pdo->prepare("DELETE FROM articles WHERE id = ? AND site_id = 2");
        $result = $stmt->execute([$articleId]);
        
        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => 'Article deleted successfully'
            ]);
        } else {
            throw new Exception('Failed to delete article');
        }
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Database error: ' . $e->getMessage()
        ]);
    }
}

// Helper function to generate slug
function generateSlug($title) {
    $slug = strtolower($title);
    
    // Turkish character replacements
    $replacements = [
        'ğ' => 'g', 'ü' => 'u', 'ş' => 's', 'ı' => 'i', 'ö' => 'o', 'ç' => 'c',
        'Ğ' => 'g', 'Ü' => 'u', 'Ş' => 's', 'İ' => 'i', 'Ö' => 'o', 'Ç' => 'c'
    ];
    
    $slug = strtr($slug, $replacements);
    
    // Remove special characters and replace spaces with hyphens
    $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);
    $slug = preg_replace('/\s+/', '-', $slug);
    $slug = preg_replace('/-+/', '-', $slug);
    $slug = trim($slug, '-');
    
    return $slug;
}
?>
