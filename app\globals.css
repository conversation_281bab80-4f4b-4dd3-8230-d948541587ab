/* Modern Typography - Optimized for performance */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Variables */
:root {
  --primary-color: #1a1a1a;
  --primary-hover: #333333;
  --accent-color: #64748b;
  --accent-hover: #475569;
  --background: #FFFFFF;
  --text: #333333;
  --text-light: #4a4a4a; /* Improved contrast from #666666 */
  --text-muted: #595959; /* New variable for better contrast */
  --border: #E5E5E5;
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
}

/* Base Styles */
@layer base {
  * {
    box-sizing: border-box;
  }
  
  body {
    @apply bg-white text-gray-900;
    font-family: 'Inter', sans-serif;
    color: var(--text);
    background: var(--background);
    -webkit-tap-highlight-color: transparent;
    overflow-x: hidden;
  }

  html {
    scroll-behavior: smooth;
  }
}

/* Component Layer */
@layer components {
  /* But<PERSON> Styles */
  .btn-primary {
    @apply bg-[var(--primary-color)] hover:bg-[var(--primary-hover)] text-white px-6 py-3 rounded-lg transition-all duration-300;
    cursor: pointer;
  }
  
  .btn-secondary {
    @apply bg-[var(--accent-color)] hover:bg-[var(--accent-hover)] text-white px-6 py-3 rounded-lg transition-all duration-300;
    cursor: pointer;
  }
  
  /* Card Styles */
  .modern-card {
    @apply bg-white rounded-xl shadow-lg border border-gray-100 transition-all duration-300 hover:shadow-xl hover:-translate-y-1;
  }

  /* Glass Effect */
  .glass-btn {
    position: relative;
    padding: 15px 30px;
    color: white;
    background: var(--glass-bg);
    border-radius: 12px;
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(30px);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    cursor: pointer;
  }

  .glass-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.15);
  }

  .glass-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: left 0.5s;
  }

  .glass-btn:hover::before {
    left: 100%;
  }

  .glass-btn-success {
    position: relative;
    padding: 15px 30px;
    color: white;
    background: linear-gradient(135deg, #64748b, #475569);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    cursor: pointer;
  }
}

/* Focus Styles */
:focus {
  outline: none !important;
}

a:focus-visible,
button:focus-visible,
input:focus-visible,
textarea:focus-visible {
  @apply ring-2 ring-slate-400 ring-offset-2 ring-offset-white rounded;
}

/* Interactive Elements */
a,
button,
[role="button"],
.interactive-element {
  cursor: pointer !important;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  color: var(--primary-color);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Animation Classes */
.fade-up {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-up.visible {
  opacity: 1;
  transform: translateY(0);
}

.fade-in {
  opacity: 0;
  transition: opacity 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
}

/* Hover Effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Form Styles */
input:focus,
textarea:focus,
select:focus {
  box-shadow: 0 0 0 3px rgba(100, 116, 139, 0.1);
  border-color: var(--accent-color);
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: var(--accent-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-hover);
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .fade-up,
  .fade-in {
    opacity: 1;
    transform: none;
  }
}

/* Utility Classes */
.text-primary {
  color: var(--primary-color);
}

.text-accent {
  color: var(--accent-color);
}

.text-muted {
  color: var(--text-muted);
}

/* High contrast text utilities */
.text-gray-400-contrast {
  color: #4a4a4a !important; /* Better contrast than default gray-400 */
}

.text-gray-500-contrast {
  color: #404040 !important; /* Better contrast than default gray-500 */
}

.bg-primary {
  background-color: var(--primary-color);
}

.bg-accent {
  background-color: var(--accent-color);
}

.border-accent {
  border-color: var(--accent-color);
}

/* Selection Color */
::selection {
  background: var(--accent-color);
  color: white;
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Hide ElevenLabs "Powered by" text - Optimized approach */
elevenlabs-convai::part(powered-by),
elevenlabs-convai [data-testid*="powered"],
elevenlabs-convai [class*="powered"],
elevenlabs-convai [class*="branding"],
elevenlabs-convai [class*="footer"],
elevenlabs-convai [aria-label*="Powered by"],
elevenlabs-convai [title*="Powered by"],
elevenlabs-convai div[style*="font-size: 12px"],
elevenlabs-convai div[style*="font-size: 10px"],
elevenlabs-convai div[style*="font-size: 11px"],
elevenlabs-convai div[style*="opacity: 0.7"],
elevenlabs-convai div[style*="opacity: 0.6"],
elevenlabs-convai div[style*="opacity: 0.5"],
elevenlabs-convai div[style*="opacity: 0.8"],
elevenlabs-convai small,
elevenlabs-convai .text-xs,
elevenlabs-convai .text-sm {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  height: 0 !important;
  width: 0 !important;
  overflow: hidden !important;
  position: absolute !important;
  left: -9999px !important;
  top: -9999px !important;
}

/* Hide bottom attribution area and common branding elements */
elevenlabs-convai > div:last-child,
elevenlabs-convai > div:nth-last-child(1),
elevenlabs-convai > div:nth-last-child(2),
elevenlabs-convai div[style*="bottom"],
elevenlabs-convai div[style*="position: absolute"][style*="bottom"],
elevenlabs-convai div[style*="position: fixed"][style*="bottom"],
elevenlabs-convai footer,
elevenlabs-convai [role="contentinfo"] {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  height: 0 !important;
  width: 0 !important;
  overflow: hidden !important;
  position: absolute !important;
  left: -9999px !important;
  top: -9999px !important;
}

/* More aggressive selectors for common patterns */
elevenlabs-convai div[style*="text-align: center"],
elevenlabs-convai div[style*="text-align:center"],
elevenlabs-convai div[style*="color: rgb(107, 114, 126)"],
elevenlabs-convai div[style*="color: rgb(156, 163, 175)"],
elevenlabs-convai div[style*="color: #6b7280"],
elevenlabs-convai div[style*="color: #9ca3af"],
elevenlabs-convai div[style*="margin-top"],
elevenlabs-convai div[style*="padding-top"],
elevenlabs-convai a[href*="elevenlabs"],
elevenlabs-convai a[href*="ElevenLabs"] {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  height: 0 !important;
  width: 0 !important;
  overflow: hidden !important;
  position: absolute !important;
  left: -9999px !important;
  top: -9999px !important;
}

/* Safe approach - let JavaScript handle text-based hiding */

/* Prose Styles for Article Content */
.prose {
  @apply text-gray-700 leading-relaxed;
}

.prose h1 {
  @apply text-3xl font-bold text-slate-900 mb-6 mt-8 first:mt-0;
}

.prose h2 {
  @apply text-2xl font-semibold text-slate-900 mb-4 mt-8 first:mt-0;
}

.prose h3 {
  @apply text-xl font-semibold text-slate-900 mb-3 mt-6;
}

.prose h4 {
  @apply text-lg font-semibold text-slate-900 mb-3 mt-6;
}

.prose p {
  @apply mb-4 leading-relaxed text-slate-700;
}

.prose ul {
  @apply mb-4 pl-6 space-y-2;
}

.prose ol {
  @apply mb-4 pl-6 space-y-2;
}

.prose li {
  @apply text-slate-700;
}

.prose blockquote {
  @apply border-l-4 border-slate-500 pl-6 py-2 mb-6 italic text-slate-700 bg-slate-50 rounded-r-lg;
}

.prose strong {
  @apply font-semibold text-slate-900;
}

.prose em {
  @apply italic;
}

.prose a {
  @apply text-slate-600 hover:text-slate-700 underline transition-colors;
}

.prose code {
  @apply bg-slate-100 text-slate-800 px-2 py-1 rounded text-sm font-mono;
}

.prose pre {
  @apply bg-slate-900 text-slate-100 p-4 rounded-lg overflow-x-auto mb-4;
}

.prose pre code {
  @apply bg-transparent text-slate-100 p-0;
}

.prose img {
  @apply rounded-lg shadow-lg mb-6;
}

.prose hr {
  @apply border-slate-200 my-8;
}

.prose table {
  @apply w-full border-collapse border border-slate-200 mb-6;
}

.prose th {
  @apply bg-slate-50 border border-slate-200 px-4 py-2 text-left font-semibold text-slate-900;
}

.prose td {
  @apply border border-slate-200 px-4 py-2 text-slate-700;
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}