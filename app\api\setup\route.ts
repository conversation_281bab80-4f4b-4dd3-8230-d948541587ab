import { createAdminUser } from '@/lib/db';
import { NextResponse } from 'next/server';

export const dynamic = 'force-static';

export async function GET() {
    try {
        await createAdminUser('admin', '<EMAIL>', 'admin', 'Admin User');
        return NextResponse.json({ message: 'Admin user created successfully' });
    } catch (error: any) {
        if (error.code === 'ER_DUP_ENTRY') {
            return NextResponse.json({ message: 'Admin user already exists' }, { status: 409 });
        }
        return NextResponse.json({ message: 'Error creating admin user', error: error.message }, { status: 500 });
    }
}
