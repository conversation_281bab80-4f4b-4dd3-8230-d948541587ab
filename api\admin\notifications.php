<?php
require_once '../config.php';

// Check admin authentication
session_start();
if (!isset($_SESSION['admin_user'])) {
    http_response_code(401);
    sendResponse(false, 'Yet<PERSON><PERSON> erişim');
}

$pdo = getDBConnection();

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {
        $notifications = [];
        
        // Get new contact messages
        $contactStmt = $pdo->prepare("
            SELECT 'contact' as type, id, full_name as name, created_at, site_id,
                   s.site_name, s.site_code
            FROM contact_messages cm
            LEFT JOIN sites s ON cm.site_id = s.id
            WHERE cm.status = 'new' AND cm.deleted = FALSE
            ORDER BY cm.created_at DESC
            LIMIT 5
        ");
        $contactStmt->execute();
        $contacts = $contactStmt->fetchAll();
        
        foreach ($contacts as $contact) {
            $notifications[] = [
                'id' => 'contact_' . $contact['id'],
                'type' => 'contact',
                'title' => 'Ye<PERSON>',
                'message' => $contact['name'] . ' tarafından yeni me<PERSON>j',
                'site_name' => $contact['site_name'],
                'site_code' => $contact['site_code'],
                'created_at' => $contact['created_at'],
                'link' => '/control-area/dashboard/contacts'
            ];
        }
        
        // Get new contact forms
        $contactFormStmt = $pdo->prepare("
            SELECT 'contact_form' as type, id, full_name as name, created_at, site_id,
                   s.site_name, s.site_code
            FROM contact_forms cf
            LEFT JOIN sites s ON cf.site_id = s.id
            WHERE cf.status = 'new' AND cf.deleted = FALSE
            ORDER BY cf.created_at DESC
            LIMIT 5
        ");
        $contactFormStmt->execute();
        $contactForms = $contactFormStmt->fetchAll();
        
        foreach ($contactForms as $form) {
            $notifications[] = [
                'id' => 'contact_form_' . $form['id'],
                'type' => 'contact',
                'title' => 'Yeni İletişim Formu',
                'message' => $form['name'] . ' tarafından yeni form',
                'site_name' => $form['site_name'],
                'site_code' => $form['site_code'],
                'created_at' => $form['created_at'],
                'link' => '/control-area/dashboard/contacts'
            ];
        }
        
        // Get new callback requests
        $callbackStmt = $pdo->prepare("
            SELECT 'callback' as type, id, full_name as name, created_at, site_id,
                   s.site_name, s.site_code
            FROM callback_requests cr
            LEFT JOIN sites s ON cr.site_id = s.id
            WHERE cr.status = 'pending' AND cr.deleted = FALSE
            ORDER BY cr.created_at DESC
            LIMIT 5
        ");
        $callbackStmt->execute();
        $callbacks = $callbackStmt->fetchAll();
        
        foreach ($callbacks as $callback) {
            $notifications[] = [
                'id' => 'callback_' . $callback['id'],
                'type' => 'callback',
                'title' => 'Yeni Geri Arama Talebi',
                'message' => $callback['name'] . ' tarafından geri arama talebi',
                'site_name' => $callback['site_name'],
                'site_code' => $callback['site_code'],
                'created_at' => $callback['created_at'],
                'link' => '/control-area/dashboard/callbacks'
            ];
        }
        
        // Get new meeting requests
        $meetingStmt = $pdo->prepare("
            SELECT 'meeting' as type, id, full_name as name, created_at, site_id,
                   s.site_name, s.site_code
            FROM meeting_requests mr
            LEFT JOIN sites s ON mr.site_id = s.id
            WHERE mr.status = 'pending' AND mr.deleted = FALSE
            ORDER BY mr.created_at DESC
            LIMIT 5
        ");
        $meetingStmt->execute();
        $meetings = $meetingStmt->fetchAll();
        
        foreach ($meetings as $meeting) {
            $notifications[] = [
                'id' => 'meeting_' . $meeting['id'],
                'type' => 'meeting',
                'title' => 'Yeni Toplantı Talebi',
                'message' => $meeting['name'] . ' tarafından toplantı talebi',
                'site_name' => $meeting['site_name'],
                'site_code' => $meeting['site_code'],
                'created_at' => $meeting['created_at'],
                'link' => '/control-area/dashboard/meetings'
            ];
        }
        
        // Sort by created_at desc
        usort($notifications, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });
        
        // Limit to 10 most recent
        $notifications = array_slice($notifications, 0, 10);
        
        sendResponse(true, 'Bildirimler başarıyla alındı', [
            'notifications' => $notifications,
            'unread_count' => count($notifications)
        ]);

    } catch (PDOException $e) {
        error_log('Notifications error: ' . $e->getMessage());
        sendResponse(false, 'Bildirimler alınırken hata oluştu');
    }

} else {
    http_response_code(405);
    sendResponse(false, 'Method not allowed');
}
