<?php
require_once '../config.php';

// Check admin authentication
session_start();
if (!isset($_SESSION['admin_user'])) {
    http_response_code(401);
    sendResponse(false, 'Yet<PERSON><PERSON> erişim');
}

$pdo = getDBConnection();

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // Get all settings
    try {
        $stmt = $pdo->query("SELECT * FROM site_settings ORDER BY setting_key");
        $settings = $stmt->fetchAll();
        
        sendResponse(true, 'Settings retrieved successfully', [
            'settings' => $settings
        ]);
    } catch (Exception $e) {
        sendResponse(false, '<PERSON>yarlar alınırken hata oluştu: ' . $e->getMessage());
    }
    
} elseif ($_SERVER['REQUEST_METHOD'] === 'PUT') {
    // Update settings
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['settings'])) {
        sendResponse(false, 'Geçersiz veri');
    }
    
    try {
        $pdo->beginTransaction();
        
        foreach ($input['settings'] as $setting) {
            if (!isset($setting['setting_key']) || !isset($setting['setting_value'])) {
                continue;
            }
            
            $stmt = $pdo->prepare("UPDATE site_settings SET setting_value = ? WHERE setting_key = ?");
            $stmt->execute([$setting['setting_value'], $setting['setting_key']]);
        }
        
        $pdo->commit();
        sendResponse(true, 'Ayarlar başarıyla güncellendi');
        
    } catch (Exception $e) {
        $pdo->rollBack();
        sendResponse(false, 'Ayarlar güncellenirken hata oluştu: ' . $e->getMessage());
    }
    
} elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Add new setting
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['setting_key']) || !isset($input['setting_value'])) {
        sendResponse(false, 'Ayar anahtarı ve değeri gerekli');
    }
    
    try {
        $stmt = $pdo->prepare("INSERT INTO site_settings (setting_key, setting_value, setting_type, description) VALUES (?, ?, ?, ?)");
        $stmt->execute([
            $input['setting_key'],
            $input['setting_value'],
            $input['setting_type'] ?? 'string',
            $input['description'] ?? ''
        ]);
        
        sendResponse(true, 'Yeni ayar eklendi');
        
    } catch (Exception $e) {
        sendResponse(false, 'Ayar eklenirken hata oluştu: ' . $e->getMessage());
    }
    
} elseif ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
    // Delete setting
    $setting_key = $_GET['key'] ?? '';
    
    if (!$setting_key) {
        sendResponse(false, 'Ayar anahtarı gerekli');
    }
    
    try {
        $stmt = $pdo->prepare("DELETE FROM site_settings WHERE setting_key = ?");
        $result = $stmt->execute([$setting_key]);
        
        if ($result && $stmt->rowCount() > 0) {
            sendResponse(true, 'Ayar silindi');
        } else {
            sendResponse(false, 'Ayar bulunamadı');
        }
        
    } catch (Exception $e) {
        sendResponse(false, 'Ayar silinirken hata oluştu: ' . $e->getMessage());
    }
    
} else {
    http_response_code(405);
    sendResponse(false, 'Method not allowed');
}
