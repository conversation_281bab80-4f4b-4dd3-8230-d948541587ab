<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../config/database.php';

try {
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetNotifications();
            break;
        case 'PUT':
            handleUpdateNotification();
            break;
        case 'POST':
            handleCreateNotification();
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Server error: ' . $e->getMessage()]);
}

function handleGetNotifications() {
    global $pdo;
    
    try {
        // Tüm bildirimleri getir (en yeniler önce)
        $stmt = $pdo->prepare("
            SELECT id, title, message, type, is_read, related_id, created_at
            FROM notifications
            ORDER BY created_at DESC
            LIMIT 50
        ");
        $stmt->execute();
        $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Okunmamış bildirim sayısı
        $stmt = $pdo->prepare("SELECT COUNT(*) as unread_count FROM notifications WHERE is_read = FALSE");
        $stmt->execute();
        $unreadCount = $stmt->fetch(PDO::FETCH_ASSOC)['unread_count'];
        
        echo json_encode([
            'success' => true,
            'data' => [
                'notifications' => $notifications,
                'unread_count' => (int)$unreadCount
            ]
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleUpdateNotification() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Notification ID is required']);
        return;
    }
    
    try {
        // Bildirimi okundu olarak işaretle
        $stmt = $pdo->prepare("UPDATE notifications SET is_read = TRUE WHERE id = ?");
        $stmt->execute([$input['id']]);
        
        if ($stmt->rowCount() > 0) {
            echo json_encode(['success' => true, 'message' => 'Notification marked as read']);
        } else {
            http_response_code(404);
            echo json_encode(['error' => 'Notification not found']);
        }
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleCreateNotification() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['title']) || !isset($input['message'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Title and message are required']);
        return;
    }
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO notifications (title, message, type, related_id)
            VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([
            $input['title'],
            $input['message'],
            $input['type'] ?? 'info',
            $input['related_id'] ?? null
        ]);
        
        $notificationId = $pdo->lastInsertId();
        
        echo json_encode([
            'success' => true,
            'data' => ['id' => $notificationId],
            'message' => 'Notification created successfully'
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
    }
}
?>
