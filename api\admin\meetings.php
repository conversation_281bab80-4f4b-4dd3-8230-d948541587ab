<?php
require_once '../config.php';

// Check admin authentication
session_start();
if (!isset($_SESSION['admin_user'])) {
    http_response_code(401);
    sendResponse(false, 'Yet<PERSON><PERSON> erişim');
}

$pdo = getDBConnection();

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // Get meetings with pagination
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = max(1, min(100, intval($_GET['limit'] ?? 10)));
    $status = $_GET['status'] ?? null;
    $offset = ($page - 1) * $limit;
    
    $whereClause = 'WHERE deleted = FALSE';
    $params = [];

    if ($status) {
        $whereClause .= ' AND status = ?';
        $params[] = $status;
    }

    // Get total count
    $countStmt = $pdo->prepare("SELECT COUNT(*) FROM meeting_requests $whereClause");
    $countStmt->execute($params);
    $totalCount = $countStmt->fetchColumn();

    // Get meetings with site information
    $stmt = $pdo->prepare("
        SELECT mr.id, mr.site_id, mr.full_name, mr.email, mr.phone, mr.company,
               mr.meeting_type, mr.preferred_date, mr.preferred_time, mr.alternative_date,
               mr.alternative_time, mr.meeting_format, mr.subject, mr.description,
               mr.status, mr.is_read, mr.created_at
        FROM meeting_requests mr
        $whereClause
        ORDER BY mr.created_at DESC
        LIMIT ? OFFSET ?
    ");
    $params[] = $limit;
    $params[] = $offset;
    $stmt->execute($params);
    $meetings = $stmt->fetchAll();

    // Add site names based on site_id
    foreach ($meetings as &$meeting) {
        if ($meeting['site_id'] == 1) {
            $meeting['site_name'] = 'Meta Analiz Group';
            $meeting['site_code'] = 'metaanaliz-group';
        } elseif ($meeting['site_id'] == 2) {
            $meeting['site_name'] = 'Meta Analiz Müşavirlik';
            $meeting['site_code'] = 'metaanaliz-musavirlik';
        } else {
            $meeting['site_name'] = 'Bilinmeyen Site';
            $meeting['site_code'] = 'unknown';
        }
    }
    
    sendResponse(true, 'Meetings retrieved successfully', [
        'meetings' => $meetings,
        'pagination' => [
            'page' => $page,
            'limit' => $limit,
            'total' => $totalCount,
            'totalPages' => ceil($totalCount / $limit)
        ]
    ]);
    
} elseif ($_SERVER['REQUEST_METHOD'] === 'PATCH') {
    // Update meeting status or mark as read
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        sendResponse(false, 'Invalid JSON input');
    }

    $id = intval($input['id'] ?? 0);
    $action = sanitizeInput($input['action'] ?? '');

    if (!$id) {
        sendResponse(false, 'ID gerekli');
    }

    if ($action === 'mark_read') {
        // Mark as read
        $stmt = $pdo->prepare("UPDATE meeting_requests SET is_read = TRUE WHERE id = ?");
        $result = $stmt->execute([$id]);

        if ($result && $stmt->rowCount() > 0) {
            sendResponse(true, 'Toplantı talebi okundu olarak işaretlendi');
        } else {
            sendResponse(false, 'Toplantı talebi bulunamadı veya güncellenemedi');
        }
    } else {
        // Update status
        $status = sanitizeInput($input['status'] ?? '');

        if (!$status) {
            sendResponse(false, 'Durum gerekli');
        }

        $validStatuses = ['pending', 'confirmed', 'completed', 'cancelled'];
        if (!in_array($status, $validStatuses)) {
            sendResponse(false, 'Geçersiz durum');
        }

        $stmt = $pdo->prepare("UPDATE meeting_requests SET status = ? WHERE id = ?");
        $result = $stmt->execute([$status, $id]);

        if ($result && $stmt->rowCount() > 0) {
            sendResponse(true, 'Durum güncellendi');
        } else {
            sendResponse(false, 'Toplantı talebi bulunamadı veya güncellenemedi');
        }
    }
    
} elseif ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
    // Soft delete meeting
    $id = intval($_GET['id'] ?? 0);

    if (!$id) {
        sendResponse(false, 'ID gerekli');
    }

    $stmt = $pdo->prepare("UPDATE meeting_requests SET deleted = TRUE WHERE id = ?");
    $result = $stmt->execute([$id]);

    if ($result && $stmt->rowCount() > 0) {
        sendResponse(true, 'Toplantı talebi silindi');
    } else {
        sendResponse(false, 'Toplantı talebi bulunamadı veya silinemedi');
    }

} else {
    http_response_code(405);
    sendResponse(false, 'Method not allowed');
}
