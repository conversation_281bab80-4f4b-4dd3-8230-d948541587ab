<?php
require_once '../config.php';

// Check admin authentication
session_start();
if (!isset($_SESSION['admin_user'])) {
    http_response_code(401);
    sendResponse(false, 'Yet<PERSON><PERSON> erişim');
}

$pdo = getDBConnection();

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // Get all sites
    try {
        $stmt = $pdo->prepare("
            SELECT id, site_code, site_name, site_url, description, is_active, created_at, updated_at
            FROM sites
            ORDER BY created_at DESC
        ");
        $stmt->execute();
        $sites = $stmt->fetchAll();

        sendResponse(true, 'Siteler başarıyla alındı', [
            'sites' => $sites
        ]);
    } catch (PDOException $e) {
        error_log('Sites fetch error: ' . $e->getMessage());
        sendResponse(false, '<PERSON><PERSON> alınırken hata oluştu');
    }

} elseif ($_SERVER['REQUEST_METHOD'] === 'PATCH') {
    // Update site
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['id'])) {
        sendResponse(false, 'Geçersiz istek');
    }
    
    $siteId = intval($input['id']);
    $siteName = isset($input['site_name']) ? sanitizeInput($input['site_name']) : null;
    $siteUrl = isset($input['site_url']) ? sanitizeInput($input['site_url']) : null;
    $description = isset($input['description']) ? sanitizeInput($input['description']) : null;
    $isActive = isset($input['is_active']) ? (bool)$input['is_active'] : null;
    
    try {
        $updateFields = [];
        $params = [];
        
        if ($siteName !== null) {
            $updateFields[] = 'site_name = ?';
            $params[] = $siteName;
        }
        
        if ($siteUrl !== null) {
            $updateFields[] = 'site_url = ?';
            $params[] = $siteUrl;
        }
        
        if ($description !== null) {
            $updateFields[] = 'description = ?';
            $params[] = $description;
        }
        
        if ($isActive !== null) {
            $updateFields[] = 'is_active = ?';
            $params[] = $isActive ? 1 : 0;
        }
        
        if (empty($updateFields)) {
            sendResponse(false, 'Güncellenecek alan bulunamadı');
        }
        
        $updateFields[] = 'updated_at = NOW()';
        $params[] = $siteId;
        
        $stmt = $pdo->prepare("UPDATE sites SET " . implode(', ', $updateFields) . " WHERE id = ?");
        $stmt->execute($params);
        
        if ($stmt->rowCount() > 0) {
            sendResponse(true, 'Site başarıyla güncellendi');
        } else {
            sendResponse(false, 'Site bulunamadı');
        }
    } catch (PDOException $e) {
        error_log('Site update error: ' . $e->getMessage());
        sendResponse(false, 'Güncelleme sırasında hata oluştu');
    }

} else {
    http_response_code(405);
    sendResponse(false, 'Method not allowed');
}
