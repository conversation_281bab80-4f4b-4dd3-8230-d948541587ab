'use client';

import Link from 'next/link';
import Image from 'next/image';
import { useState, useEffect } from 'react';

interface HeaderProps {
  currentPage?: string;
}

export default function Header({ currentPage }: HeaderProps) {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const isHomePage = currentPage === 'home' || !currentPage;

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isHomePage
          ? isScrolled ? 'bg-white' : 'bg-transparent'
          : 'bg-white'
      }`}
    >
      <nav className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link href="/" className="relative z-10">
            <Image
              src="/mgsam.webp"
              alt="MGSAM Logo"
              width={140}
              height={45}
              className={`transition-all duration-300 ${
                isHomePage
                  ? isScrolled ? '' : 'brightness-0 invert'
                  : ''
              }`}
              priority
              sizes="140px"
            />
          </Link>

          {/* Desktop Menu */}
          <div className="hidden md:flex items-center space-x-8">
            <Link
              href="/"
              className={`text-sm font-medium transition-all duration-300 hover:opacity-70
                ${isHomePage
                  ? isScrolled ? 'text-gray-800' : 'text-white'
                  : 'text-gray-800'
                }
                ${currentPage === 'home' ? 'border-b-2 border-primary' : ''}`}
            >
              Ana Sayfa
            </Link>
            <div className="relative group">
              <button
                className={`text-sm font-medium transition-all duration-300 hover:opacity-70 flex items-center
                  ${isHomePage
                    ? isScrolled ? 'text-gray-800' : 'text-white'
                    : 'text-gray-800'
                  }`}
              >
                Hakkımızda
                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              <div className="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300">
                <Link
                  href="/hedef-ve-ilkelerimiz"
                  className="block px-4 py-2 text-sm text-gray-800 hover:bg-gray-100 transition-all duration-300"
                >
                  Hedef ve İlkelerimiz
                </Link>
                <Link
                  href="/farkliliklarimiz"
                  className="block px-4 py-2 text-sm text-gray-800 hover:bg-gray-100 transition-all duration-300"
                >
                  Farklılıklarımız
                </Link>
              </div>
            </div>
            <Link
              href="/makaleler"
              className={`text-sm font-medium transition-all duration-300 hover:opacity-70
                ${isHomePage
                  ? isScrolled ? 'text-gray-800' : 'text-white'
                  : 'text-gray-800'
                }
                ${currentPage === 'makaleler' ? 'border-b-2 border-primary' : ''}`}
            >
              Makalelerimiz
            </Link>
            <Link
              href="/iletisim"
              className={`text-sm font-medium transition-all duration-300 hover:opacity-70
                ${isHomePage
                  ? isScrolled ? 'text-gray-800' : 'text-white'
                  : 'text-gray-800'
                }
                ${currentPage === 'iletisim' ? 'border-b-2 border-primary' : ''}`}
            >
              İletişim
            </Link>
            <Link
              href="/toplanti-planla"
              className={`text-sm font-medium transition-all duration-300 hover:opacity-70
                ${isHomePage
                  ? isScrolled ? 'text-gray-800' : 'text-white'
                  : 'text-gray-800'
                }
                ${currentPage === 'toplanti-planla' ? 'border-b-2 border-primary' : ''}`}
            >
              Proje Toplantısı Planlayın
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className={`md:hidden relative w-10 h-10 flex flex-col items-center justify-center transition-opacity duration-300 ${
              isMobileMenuOpen ? 'opacity-0 pointer-events-none' : 'opacity-100 z-10'
            }`}
            aria-label={isMobileMenuOpen ? "Menüyü kapat" : "Menüyü aç"}
            aria-expanded={isMobileMenuOpen}
          >
            <span className={`block w-6 h-0.5 transition-all duration-300 ${
              isHomePage
                ? isScrolled ? 'bg-gray-800' : 'bg-white'
                : 'bg-gray-800'
            }`}></span>
            <span className={`block w-6 h-0.5 my-1 transition-all duration-300 ${
              isHomePage
                ? isScrolled ? 'bg-gray-800' : 'bg-white'
                : 'bg-gray-800'
            }`}></span>
            <span className={`block w-6 h-0.5 transition-all duration-300 ${
              isHomePage
                ? isScrolled ? 'bg-gray-800' : 'bg-white'
                : 'bg-gray-800'
            }`}></span>
          </button>
        </div>

        {/* Mobile Menu */}
        <div
          className={`fixed inset-0 bg-white z-0 transform transition-transform duration-300 ease-in-out ${
            isMobileMenuOpen ? 'translate-x-0' : 'translate-x-full'
          } md:hidden`}
        >
          {/* Mobile Menu Header - Logo gizli, sadece hamburger ikonu */}
          <div className="flex items-center justify-end px-6 py-4 border-b border-gray-200">
            <button
              onClick={() => setIsMobileMenuOpen(false)}
              className="w-10 h-10 flex flex-col items-center justify-center"
              aria-label="Menüyü kapat"
            >
              <span className="block w-6 h-0.5 bg-gray-800"></span>
              <span className="block w-6 h-0.5 my-1 bg-gray-800"></span>
              <span className="block w-6 h-0.5 bg-gray-800"></span>
            </button>
          </div>

          <div className="pt-6 px-6">
            <Link 
              href="/"
              className="block py-3 text-lg font-medium text-gray-800 border-b border-gray-200"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Ana Sayfa
            </Link>
            <div className="py-3 border-b border-gray-200">
              <span className="block text-lg font-medium text-gray-800 mb-2">Hakkımızda</span>
              <Link
                href="/hedef-ve-ilkelerimiz"
                className="block py-2 pl-4 text-gray-700"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Hedef ve İlkelerimiz
              </Link>
              <Link
                href="/farkliliklarimiz"
                className="block py-2 pl-4 text-gray-700"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Farklılıklarımız
              </Link>
            </div>
            <Link
              href="/iletisim"
              className="block py-3 text-lg font-medium text-gray-800 border-b border-gray-200"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              İletişim
            </Link>
            <Link
              href="/toplanti-planla"
              className="block py-3 text-lg font-medium text-gray-800"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Proje Toplantısı Planlayın
            </Link>
          </div>
        </div>
      </nav>
    </header>
  );
}