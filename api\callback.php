<?php
session_start();
require_once 'config.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, 'Sadece POST istekleri kabul edilir.');
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

// Validate required fields
$requiredFields = ['name', 'surname', 'phone'];
foreach ($requiredFields as $field) {
    if (empty($input[$field])) {
        sendResponse(false, ucfirst($field) . ' alanı zorunludur.');
    }
}

// Sanitize inputs
$name = trim($input['name']);
$surname = trim($input['surname']);
$phone = trim($input['phone']);
$preferredTime = isset($input['preferredTime']) ? trim($input['preferredTime']) : 'morning';
$message = isset($input['message']) ? trim($input['message']) : '';

// Validate name and surname
$minLength = 2;
$maxLength = 50;

if (strlen($name) < $minLength || strlen($name) > $maxLength) {
    sendResponse(false, 'İsim 2-50 karakter arasında olmalıdır.');
}

if (strlen($surname) < $minLength || strlen($surname) > $maxLength) {
    sendResponse(false, 'Soyisim 2-50 karakter arasında olmalıdır.');
}

// Validate phone
if (!validatePhone($phone)) {
    sendResponse(false, 'Geçerli bir telefon numarası giriniz.');
}

// Validate preferred time
$validTimes = ['morning', 'afternoon', 'evening'];
if (!in_array($preferredTime, $validTimes)) {
    $preferredTime = 'morning';
}

// Check for spam (same phone number in last 5 minutes)
try {
    $stmt = $pdo->prepare("
        SELECT COUNT(*) FROM callback_requests
        WHERE phone = ? AND created_at > DATE_SUB(NOW(), INTERVAL 5 MINUTE)
    ");
    $stmt->execute([$phone]);
    $recentCount = $stmt->fetchColumn();
    
    if ($recentCount > 0) {
        sendResponse(false, 'Bu telefon numarası ile son 5 dakika içinde zaten bir talep gönderilmiş. Lütfen bekleyiniz.');
    }
    
    // Get client info
    $ipAddress = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
    
    // Insert callback request
    $source = 'mgsam'; // Kaynak: MGSAM
    $stmt = $pdo->prepare("
        INSERT INTO callback_requests (
            source, name, surname, phone, preferred_time, message,
            created_at, status, ip_address, user_agent
        ) VALUES (?, ?, ?, ?, ?, ?, NOW(), 'new', ?, ?)
    ");

    $result = $stmt->execute([
        $source, $name, $surname, $phone, $preferredTime, $message, $ipAddress, $userAgent
    ]);
    
    if ($result) {
        $callbackId = $pdo->lastInsertId();
        
        // Send notification email to admin
        $emailSent = sendCallbackNotificationEmail($name, $surname, $phone, $preferredTime, $message, $callbackId);
        
        sendResponse(true, 'Geri arama talebiniz başarıyla alındı. En kısa sürede sizinle iletişime geçeceğiz.', [
            'id' => $callbackId,
            'email_sent' => $emailSent
        ]);
    } else {
        sendResponse(false, 'Talep gönderilirken bir hata oluştu. Lütfen tekrar deneyiniz.');
    }
    
} catch (PDOException $e) {
    error_log("Callback request error: " . $e->getMessage());
    sendResponse(false, 'Sistem hatası oluştu. Lütfen daha sonra tekrar deneyiniz.');
}

function sendCallbackNotificationEmail($name, $surname, $phone, $preferredTime, $message, $callbackId) {
    $to = ADMIN_EMAIL;
    $subject = "Yeni Geri Arama Talebi - " . $name . " " . $surname;
    
    $timeLabels = [
        'morning' => 'Sabah (09:00-12:00)',
        'afternoon' => 'Öğleden Sonra (13:00-17:00)',
        'evening' => 'Akşam (18:00-20:00)'
    ];
    
    $preferredTimeLabel = $timeLabels[$preferredTime] ?? 'Belirtilmemiş';
    
    $messageBody = "
    <html>
    <head>
        <meta charset='UTF-8'>
        <title>Yeni Geri Arama Talebi</title>
    </head>
    <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
        <div style='max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 10px;'>
            <h2 style='color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;'>
                🔔 Yeni Geri Arama Talebi
            </h2>
            
            <div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                <h3 style='margin-top: 0; color: #495057;'>Talep Bilgileri:</h3>
                <p><strong>Talep ID:</strong> #$callbackId</p>
                <p><strong>Ad Soyad:</strong> $name $surname</p>
                <p><strong>Telefon:</strong> $phone</p>
                <p><strong>Tercih Edilen Zaman:</strong> $preferredTimeLabel</p>
                <p><strong>Tarih:</strong> " . date('d.m.Y H:i') . "</p>
            </div>
            
            " . (!empty($message) ? "
            <div style='background-color: #e8f4f8; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                <h3 style='margin-top: 0; color: #17a2b8;'>Mesaj:</h3>
                <p style='font-style: italic;'>$message</p>
            </div>
            " : "") . "
            
            <div style='text-align: center; margin-top: 30px;'>
                <a href='" . SITE_URL . "/control-area/dashboard/callbacks'
                   style='background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;'>
                    Admin Panelde Görüntüle
                </a>
            </div>
            
            <hr style='margin: 30px 0; border: none; border-top: 1px solid #eee;'>
            <p style='font-size: 12px; color: #6c757d; text-align: center;'>
                Bu e-posta Meta Analiz Group web sitesinden otomatik olarak gönderilmiştir.
            </p>
        </div>
    </body>
    </html>
    ";
    
    $headers = [
        'MIME-Version: 1.0',
        'Content-type: text/html; charset=UTF-8',
        'From: ' . SITE_NAME . ' <' . NOREPLY_EMAIL . '>',
        'Reply-To: ' . NOREPLY_EMAIL,
        'X-Mailer: PHP/' . phpversion()
    ];
    
    return mail($to, $subject, $messageBody, implode("\r\n", $headers));
}
