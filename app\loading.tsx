'use client';

import Image from 'next/image';
import { useEffect, useState } from 'react';

export default function Loading() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <div className="fixed inset-0 bg-white flex flex-col items-center justify-center z-50">
      <div className="relative mb-8">
        <Image
          src="/meta_group_logo.webp"
          alt="Meta Analiz Group Logo"
          width={240}
          height={60}
          className="animate-pulse"
        />
      </div>
      <div className="flex items-center space-x-2">
        <div className="text-2xl text-slate-600 font-light">Yükleniyor</div>
        <div className="flex space-x-1">
          <div className="w-2 h-2 bg-slate-600 rounded-full animate-[bounce_1s_infinite_-0.3s]"></div>
          <div className="w-2 h-2 bg-slate-600 rounded-full animate-[bounce_1s_infinite_-0.2s]"></div>
          <div className="w-2 h-2 bg-slate-600 rounded-full animate-[bounce_1s_infinite]"></div>
        </div>
      </div>
    </div>
  );
}