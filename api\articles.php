<?php
// MGSAM Makale API endpoint'i
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Preflight request için
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config.php';

try {
    $pdo = getDBConnection();
    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method === 'GET') {
        handleGetArticles($pdo);
    } else {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleGetArticles($pdo) {
    try {
        // Query parametreleri
        $limit = (int)($_GET['limit'] ?? 10);
        $offset = (int)($_GET['offset'] ?? 0);
        $search = $_GET['search'] ?? '';
        $category = $_GET['category'] ?? '';
        $slug = $_GET['slug'] ?? '';
        $id = (int)($_GET['id'] ?? 0);

        // Eğer ID varsa tek makale getir
        if ($id > 0) {
            $query = "
                SELECT
                    id, title, slug, content, excerpt, author, category,
                    created_at, published_at
                FROM articles
                WHERE id = ? AND status = 'published' AND site_code = 'mgsam'
            ";

            $stmt = $pdo->prepare($query);
            $stmt->execute([$id]);
            $article = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($article) {
                // HTML entity'leri decode et
                $article['title'] = html_entity_decode($article['title'], ENT_QUOTES, 'UTF-8');
                $article['content'] = html_entity_decode($article['content'], ENT_QUOTES, 'UTF-8');
                $article['excerpt'] = html_entity_decode($article['excerpt'], ENT_QUOTES, 'UTF-8');

                sendResponse(true, 'Article retrieved successfully', $article);
            } else {
                http_response_code(404);
                sendResponse(false, 'Article not found');
            }
            return;
        }

        // Eğer slug varsa tek makale getir
        if (!empty($slug)) {
            $query = "
                SELECT
                    id, title, slug, content, excerpt, author, category,
                    created_at, published_at
                FROM articles
                WHERE slug = ? AND status = 'published' AND site_code = 'mgsam'
            ";
            
            $stmt = $pdo->prepare($query);
            $stmt->execute([$slug]);
            $article = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($article) {
                // HTML entity'leri decode et
                $article['title'] = html_entity_decode($article['title'], ENT_QUOTES, 'UTF-8');
                $article['content'] = html_entity_decode($article['content'], ENT_QUOTES, 'UTF-8');
                $article['excerpt'] = html_entity_decode($article['excerpt'], ENT_QUOTES, 'UTF-8');

                sendResponse(true, 'Article retrieved successfully', $article);
            } else {
                http_response_code(404);
                sendResponse(false, 'Article not found');
            }
            return;
        }
        
        // Makale listesi
        $whereConditions = ["status = 'published'", "site_code = 'mgsam'"];
        $params = [];
        
        // Search filtresi
        if (!empty($search)) {
            $whereConditions[] = "(title LIKE ? OR content LIKE ? OR excerpt LIKE ?)";
            $searchTerm = "%$search%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        // Category filtresi
        if (!empty($category)) {
            $whereConditions[] = "category = ?";
            $params[] = $category;
        }
        
        $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        
        // Count query
        $countQuery = "SELECT COUNT(*) as total FROM articles $whereClause";
        $countStmt = $pdo->prepare($countQuery);
        $countStmt->execute($params);
        $total = $countStmt->fetch()['total'];
        
        // Main query
        $query = "
            SELECT
                id, title, slug, excerpt, author, category,
                created_at, published_at
            FROM articles
            $whereClause
            ORDER BY published_at DESC
            LIMIT ? OFFSET ?
        ";
        
        $params[] = $limit;
        $params[] = $offset;
        
        $stmt = $pdo->prepare($query);
        $stmt->execute($params);
        $articles = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // HTML entity'leri decode et
        foreach ($articles as &$article) {
            $article['title'] = html_entity_decode($article['title'], ENT_QUOTES, 'UTF-8');
            $article['excerpt'] = html_entity_decode($article['excerpt'], ENT_QUOTES, 'UTF-8');
        }
        
        // Kategorileri de getir
        $categoriesQuery = "
            SELECT DISTINCT category, COUNT(*) as count
            FROM articles
            WHERE status = 'published' AND site_code = 'mgsam'
            GROUP BY category
            ORDER BY category
        ";
        $categoriesStmt = $pdo->prepare($categoriesQuery);
        $categoriesStmt->execute();
        $categories = $categoriesStmt->fetchAll(PDO::FETCH_ASSOC);
        
        sendResponse(true, 'Articles retrieved successfully', [
            'articles' => $articles,
            'categories' => $categories,
            'total' => $total,
            'limit' => $limit,
            'offset' => $offset
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        sendResponse(false, 'Database error: ' . $e->getMessage());
    }
}
?>
